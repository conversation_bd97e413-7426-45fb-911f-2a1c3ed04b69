# bsc-triangular-tx

A TypeScript library

## Installation

```bash
npm install bsc-triangular-tx
```

## Usage

```typescript
import { greet } from 'bsc-triangular-tx';

console.log(greet('World')); // Hello, World!
```

## Development

```bash
# Install dependencies
bun install

# Build
bun run build

# Develop with watch mode
bun run dev

# Run tests
bun run test
```

## Contributing

Please see [CONTRIBUTING.md](./CONTRIBUTING.md) for contribution guidelines.

## License

MIT
