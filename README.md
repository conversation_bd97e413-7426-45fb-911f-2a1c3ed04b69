AI 驱动的自动化视频剪辑项目
基于 数据库驱动 与 三层深度分析 的智能视频剪辑工作流

本项目实现了一个完整的自动化视频剪辑解决方案。它通过对视频进行全局（顶层）、区块（宏观）和场景（微观）的三层深度分析，将海量分析数据高效存入 SQLite 数据库。结合外部知识检索，利用 AI 生成富有洞察力的叙事大纲，再依据大纲从数据库中精确检索素材、生成文案并最终合成精华短视频。

📖 核心流程
工作流被重新规划为四个逻辑清晰的阶段：

🔬 三层深度分析与数据库构建

🌐 外部资料增强

✍️ 融合知识生成全局大纲

🎬 自动化生产与合成

🔬 阶段一：三层深度分析与数据库构建
目标： 对视频进行全面的、自下而上的三层级解析，并将所有结构化数据持久化到 SQLite 数据库中，形成一个可查询、可管理、可扩展的分析中心。

1.1 数据库设计 (SQLite)
为取代庞大且低效的 JSON 文件，我们设计一个 SQLite 数据库 analysis.db，包含以下核心表：

video_meta: 存储视频全局信息和最终的顶层总结。

id (INTEGER PRIMARY KEY)

video_name (TEXT)

total_duration (REAL)

global_summary (TEXT)

key_themes (TEXT)

chunks: 存储宏观区块分析。

id (INTEGER PRIMARY KEY)

video_id (INTEGER, FOREIGN KEY)

start_time (REAL)

end_time (REAL)

chunk_summary (TEXT)

key_entities (TEXT)

scenes: 存储最精细的微观场景分析。

id (INTEGER PRIMARY KEY)

chunk_id (INTEGER, FOREIGN KEY)

start_time (REAL)

end_time (REAL)

visual_description (TEXT)

action (TEXT)

emotion (TEXT)

dialogue (TEXT)

sound_events (TEXT)

embedding_vector (BLOB) - 可选，用于语义搜索

1.2 处理流程 (自下而上构建)
全局镜头检测: 使用 PySceneDetect 对整个长视频进行镜头边界检测，将每个镜头的 start_time 和 end_time 作为基础记录插入到 scenes 表中。

微观分析 (填充 scenes 表):

程序遍历 scenes 表中所有记录。

对每条记录，提取对应的视频片段（通常几秒长），提交给 SCENE_ANALYSIS_MODEL。

核心 Prompt (微观):

你是一个电影分析镜头语言的专家。请以 JSON 格式，精炼分析这段几秒钟的视频。

- 视觉描述 (visual_description): 场景环境，构图和光线。
- 核心动作 (action): 画面中的主要动作或事件。
- 人物情绪 (emotion): 通过面部表情和肢体语言判断。
- 对话文本 (dialogue): 将所有语音转为文字。
- 关键视觉对象 (key_objects): 列出画面中重要的物体。
- 声音事件 (sound_events): 识别背景音乐、笑声、爆炸声等。

视频片段: [传入视频数据]

用返回的结构化数据更新 scenes 表中对应的行。

宏观分析 (填充 chunks 表):

程序根据 CHUNK_DURATION_MINUTES 设置，将 scenes 表中的记录进行逻辑分组，并创建 chunks 记录。

对于每个 chunk，从 scenes 表中查询其包含的所有微观分析数据。

将这些微观分析的文本描述汇总，提交给 CHUNK_SUMMARY_MODEL。

核心 Prompt (宏观):

你是一位故事编辑。以下是一系列按时间排序的镜头描述，共同构成了一个约 15 分钟的视频章节。请：

1. 撰写一段连贯的、概括性的章节摘要 (chunk_summary)。
2. 提炼出本章节中出现的关键人物、地点、事件等实体 (key_entities)。

镜头描述列表: [传入该区块所有镜头的分析文本]

用返回的结果更新 chunks 表。

全局分析 (填充 video_meta 表):

查询 chunks 表中所有的宏观摘要。

将所有宏观摘要汇总，提交给 FINAL_OUTLINE_MODEL。

核心 Prompt (全局):

你是一位资深制作人。以下是整部影片按章节划分的摘要。请基于此，给出整部影片的：

1. 一句话核心摘要 (global_summary)。
2. 3-5 个关键主题 (key_themes)。

章节摘要列表: [传入所有区块的摘要文本]

用返回结果更新 video_meta 表。

🌐 阶段二：外部资料增强
目标： 通过搜集网络公开信息，为视频内容提供更深层次的背景和上下文，为 AI 的“再创作”提供丰富的外部知识库。

2.1 数据库表设计 (research table)
在 analysis.db 中新增一张表用于存储外部研究资料：

research:

id (INTEGER PRIMARY KEY)

video_id (INTEGER, FOREIGN KEY)

related_entity (TEXT) - 关联的关键实体，如 "埃菲尔铁塔"

source_url (TEXT) - 资料来源网址

retrieved_content (TEXT) - 从网页获取的原始内容摘要

ai_summary (TEXT) - 经过 AI 提炼的核心知识点

2.2 详细处理流程
实体提取与查询生成:

程序执行一个 SQL 查询，从 chunks 表中提取所有不重复的 key_entities。

SELECT DISTINCT key_entities FROM chunks WHERE video_id = ?;

对每个实体，生成多个探索性的搜索关键词。例如，对于实体“星际穿越”，生成 ["《星际穿越》导演克里斯托弗·诺兰访谈", "《星际穿越》科学背景解析", "电影《星际穿越》影评"]。

网络信息检索:

使用生成的关键词，调用搜索引擎 API（如 Google Search API）进行批量搜索。

获取返回结果中的网页标题、摘要和 URL，优先选择看起来更权威的来源（如知名媒体、百科、官方网站）。

内容提取与 AI 总结:

对于每个高价值的 URL，程序会尝试访问并提取其主要文本内容（可使用 BeautifulSoup 等库）。

将提取的文本内容提交给一个 AI 模型，进行信息提炼。

核心 Prompt (外部资料总结):

你是一个研究助理。请从以下网页内容中，提炼出与 "[相关实体]" 相关的核心事实、有趣见解或关键引述。总结应客观、精炼，不超过 200 字。

网页内容: [传入抓取的网页文本]

将来源 URL、实体、原始摘要和 AI 总结存入 research 表。

✍️ 阶段三：融合知识生成全局大纲
目标： 将视频的内部理解（来自阶段一）与外部知识（来自阶段二）深度融合，创造一个有独特视角和深度的最终叙事大纲。

3.1 数据库表设计 (outlines & chapters tables)
为存储最终的大纲，我们设计两张新表：

outlines: 存储每个视频生成的大纲的元信息。

id (INTEGER PRIMARY KEY)

video_id (INTEGER, FOREIGN KEY)

theme (TEXT) - AI 生成的核心主题

created_at (DATETIME)

chapters: 存储大纲中的每一个章节。

id (INTEGER PRIMARY KEY)

outline_id (INTEGER, FOREIGN KEY)

chapter_number (INTEGER) - 章节序号

title (TEXT) - 章节标题

summary (TEXT) - 章节摘要，将用于指导后续的片段检索和文案生成

3.2 详细处理流程
数据准备:

程序从 analysis.db 中查询所需的所有信息：

SELECT chunk_summary FROM chunks WHERE video_id = ? ORDER BY start_time;

SELECT related_entity, ai_summary FROM research WHERE video_id = ?;

融合与大纲生成:

将查询到的内部摘要和外部知识整合成一个结构化的文本，提交给 FINAL_OUTLINE_MODEL。

核心 Prompt (融合知识生成大纲):

你是一位顶尖的纪录片导演。请基于以下两份核心资料，为一部约 5 分钟的精华短片，创作一个引人入胜的叙事大纲。

### 资料一：视频内容的内部摘要（按时间顺序）

[此处按顺序插入所有区块(chunk)的摘要]

### 资料二：相关的外部背景研究

[此处按条目插入所有相关的研究(research)总结]

你的任务是：

1.  提炼一个深刻、独特的核心主题 (theme)。
2.  将故事划分为 5-7 个章节，为每个章节构思一个吸引人的标题 (title) 和一段概括性的内容摘要 (summary)。
3.  你的大纲不应只是简单复述内容，而要巧妙地将外部研究的观点和信息融入章节摘要中，提供新的视角。
4.  请以 JSON 格式返回，包含`theme`和一个`chapters`列表，列表中每个对象都有`chapter_number`, `title`, 和 `summary`。

存储大纲:

程序解析 AI 返回的 JSON，首先在 outlines 表中创建一个新条目，获取 outline_id。

然后，遍历 JSON 中的 chapters 列表，将每个章节的数据插入到 chapters 表中，并关联上一步获取的 outline_id。

🎬 阶段四：自动化生产与合成
目标： 将抽象的大纲转化为具体的、可观看的视频成品，全流程自动化。

4.1 片段检索 (数据库查询)
此步骤将大纲中每个章节的描述性文本，转化为精确的数据库查询。

查询意图理解:

遍历 chapters 表中的每一条记录。

对于每个章节的 summary，调用一次 AI 模型来“翻译”成可执行的搜索条件。

核心 Prompt (摘要转查询):

你是一个智能检索助手。请将下面的章节描述，转换成一组用于在视频场景数据库中搜索的关键词和条件。关注动作、情绪、和具体对象。

章节描述: "在暴风雨的考验下，团队成员从最初的恐惧转为紧密协作，展现出非凡的勇气。"

请以 JSON 格式返回搜索条件:
{
"required_visuals": ["暴风雨", "船", "特写镜头"],
"required_emotions": ["恐惧", "坚定", "协作"],
"required_actions": ["掌舵", "呼喊", "救援"],
"dialogue_keywords": ["坚持住", "我们能行"]
}

执行数据库查询:

程序根据 AI 返回的 JSON，动态构建 SQL 查询语句。

示例 SQL (动态构建):

SELECT id, start_time, end_time FROM scenes
WHERE
(visual_description LIKE '%暴风雨%' OR visual_description LIKE '%船%')
AND (emotion = '恐惧' OR emotion = '坚定')
AND dialogue LIKE '%坚持住%';

如果数据库支持向量搜索，则可以将章节 summary 文本转换为 embedding，在 scenes 表的 embedding_vector 列中进行高效的语义相似度搜索。

4.2 文案创作与配音
生成逐章文案:

将检索到的场景片段信息（主要是它们的 visual_description 和 dialogue），连同章节的 title 和 summary，一起提交给 AI。

核心 Prompt (文案创作):

你是一位金牌旁白撰稿人。请为以下视频章节撰写一段生动、富有感染力的解说词。

章节标题: "风暴中的誓言"
章节摘要: "在暴风雨的考验下，团队成员从最初的恐惧转为紧密协作，展现出非凡的勇气。"
将使用的镜头描述:

- 镜头 1: [远景，乌云密布，船在巨浪中颠簸...]
- 镜头 2: [人物 A 面部特写，眼神充满恐惧...]
- 镜头 3: [人物 B 大声呼喊，指挥大家稳住船舵...]

你的文案需要将这些离散的镜头串联成一个激动人心的故事。

文本转语音 (TTS):

将生成的文案通过 TTS 服务（如 Google Cloud TTS, ElevenLabs API）转换为 MP3 音频文件。

4.3 最终合成
素材准备:

根据片段检索的结果，使用 MoviePy 从原始视频中精确裁剪出所有需要的视频片段。

准备好上一步生成的解说词 MP3 文件。

音视频合成:

使用 MoviePy 创建一个新的视频序列。

按顺序拼接视频片段，并可在片段之间添加交叉淡化等简单的转场效果。

将解说音频和背景音乐（可选）合成到视频的音轨上。

音频混合: 如果添加了背景音乐，需要实现自动音量闪避 (Audio Ducking)，即当解说音轨有声音时，背景音乐的音量自动降低。

最终输出: 一个完整的、音画同步的精华短视频 final_cut.mp4。

🛠 技术栈
视频处理： Python + MoviePy + OpenCV + PySceneDetect

数据持久化: SQLite

AI 能力：

核心视频理解： Google Gemini API (2.5 Pro 或更新版本)

网络检索: 搜索引擎 API (如 Google Search API)

文字转语音： Google Cloud TTS / ElevenLabs API

数据格式 (交互): JSON

✨ 关键优势
三层深度理解: 结合全局、宏观与微观分析，既有大局观，又不失细节。

数据驱动: 使用 SQLite 管理所有分析数据，高效、可扩展、易于查询和维护。

精准剪辑: 基于对每个镜头的精细化数据进行数据库查询，素材检索和最终剪辑的精度大大提高。

人机协同: AI 负责繁重的分析与创作，人类可在关键节点（如审阅大纲）进行干预，确保创意方向。

模块化设计: 各阶段解耦，可独立优化，例如更换更强的 AI 模型或引入更丰富的知识源。

🚀 快速开始
环境准备

pip install -r requirements.txt
cp .env.example .env # 配置所有 API keys

运行流程

python main.py --input video.mp4 --output final_cut.mp4

📁 项目结构
auto-cutter/
├── main.py # 主程序入口
├── .env.example # 环境变量示例
├── .env # 环境配置 (本地)
├── data/
│ └── analysis.db # SQLite 数据库，存储所有分析和研究数据
├── output/
│ ├── outline.json # 生成的大纲文件（或存入数据库）
│ └── final_cut.mp4 # 最终输出视频
└── ...

🤝 贡献
欢迎提交 Issue 和 Pull Request 来改进项目！

📄 许可证
MIT License
