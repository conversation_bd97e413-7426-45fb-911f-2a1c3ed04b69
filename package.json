{"name": "bsc-triangular-tx", "version": "0.1.0", "description": "A TypeScript library", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "packageManager": "bun@1.2.14", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "dev": "bun run src/index.ts", "compile-contract": "bun run scripts/compile.ts", "deploy": "bun run scripts/deploy.ts", "check-once": "bun run src/index.ts check-once", "check-once-enhanced": "bun run src/index.ts check-once-enhanced", "check-once-parallel": "bun run src/index.ts check-once-parallel", "check-once-super-parallel": "bun run src/index.ts check-once-super-parallel", "check-once-websocket": "bun run src/index.ts check-once-websocket", "check-once-ultra-optimized": "bun run src/index.ts check-once-ultra-optimized", "check-once-super-optimized-v2": "bun run src/index.ts check-once-super-optimized-v2", "start-event-driven-monitoring": "bun run src/index.ts start-event-driven-monitoring", "benchmark-performance": "bun run src/index.ts benchmark-performance", "benchmark-performance-v2": "bun run src/index.ts benchmark-performance-v2", "start-monitoring": "bun run src/index.ts start-monitoring", "test-prices": "bun run src/index.ts test-prices", "test-v3": "bun run src/index.ts test-v3", "test-unified": "bun run src/index.ts test-unified", "router-diagnostic": "bun run src/index.ts router-diagnostic", "test-smart-router": "bun run src/index.ts test-smart-router", "test-official-router": "bun run src/index.ts test-official-router", "diagnose-v3-pool": "bun run src/index.ts diagnose-v3-pool", "test-bank-arbitrage": "bun run src/index.ts test-bank-arbitrage", "test-bank-pool": "bun run src/index.ts test-bank-pool", "test-direct-v3-swap": "bun run src/index.ts test-direct-v3-swap", "test-enhanced-direct-swap": "bun run src/index.ts test-enhanced-direct-swap", "test-usd1-bank-pool": "bun run src/index.ts test-usd1-bank-pool", "diagnose-usd1-bank-pool": "bun run src/index.ts diagnose-usd1-bank-pool", "diagnose-pool-token-order": "bun run src/index.ts diagnose-pool-token-order", "validate-price-calculations": "bun run src/index.ts validate-price-calculations", "test-usd1-bank-trading": "bun run src/index.ts test-usd1-bank-trading", "test-bnb-bank-usd1-arbitrage": "bun run src/index.ts test-bnb-bank-usd1", "test-v3-router": "bun run src/index.ts test-v3-router", "check-config": "bun run src/index.ts check-config", "diagnose-dex": "bun run src/index.ts diagnose-dex", "test-direct-quoter": "bun run src/index.ts test-direct-quoter", "lint:fix": "biome check . --fix --unsafe", "lint": "biome check .", "format": "biome format .", "format:fix": "biome format --write .", "tsc": "tsc --noEmit", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage", "release": "bumpp --commit --push --tag", "publish:ci": "bun publish --access public --no-git-checks", "prepare": "husky"}, "dependencies": {"@pancakeswap/sdk": "^5.8.14", "@pancakeswap/smart-router": "^7.2.4", "@pancakeswap/tokens": "^0.7.4", "dotenv": "^16.5.0", "solc": "^0.8.30", "viem": "^2.30.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/bun": "latest", "@types/node": "^22.15.21", "bumpp": "^10.1.0", "bunup": "^0.5.6", "husky": "^9.1.7", "npm-run-all2": "^8.0.3", "oxlint": "^0.16.11", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.1.2"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/bene/bsc-triangular-tx.git"}, "homepage": "https://github.com/bene/bsc-triangular-tx#readme", "private": true}