# 直接 V3 池子 Swap 功能指南

## 概述

本项目现在支持直接调用 PancakeSwap V3 池子合约的 `swap` 函数，绕过 Quoter 和 Router，实现更直接的代币交换。这个功能特别适用于 BANK/BNB 等流动性较低的代币对。

## 功能特点

### ✅ 已实现功能

1. **完整的池子 ABI 支持**

   - 使用用户提供的完整 BANK/BNB 池子合约 ABI
   - 支持所有标准 V3 池子函数调用

2. **真正的直接 swap 调用**

   - 直接调用池子的 `swap` 函数
   - 包含简化的回调处理逻辑
   - 支持精确输入和精确输出模式

3. **准确的价格计算**

   - 基于 `sqrtPriceX96` 的实时价格计算
   - 支持 token0/token1 双向价格显示
   - 价格影响估算

4. **完整的安全检查**
   - 代币余额验证
   - 授权额度检查
   - 滑点保护
   - Gas 估算

### 🔧 核心组件

#### 1. DirectV3PoolSwapService

主要的服务类，提供以下功能：

```typescript
// 获取池子状态
async getPoolState(poolAddress: Address): Promise<PoolState | null>

// 计算当前价格
async calculatePrice(poolAddress: Address): Promise<PriceCalculation | null>

// 模拟 swap
async simulateSwap(
  poolAddress: Address,
  zeroForOne: boolean,
  amountSpecified: bigint
): Promise<SwapSimulation | null>

// 执行真正的 swap
async executeSwap(params: DirectSwapParams): Promise<SwapResult>
```

#### 2. V3SwapCallback 智能合约

专门处理 V3 池子 swap 回调的智能合约：

```solidity
contract V3SwapCallback is IUniswapV3SwapCallback {
    function uniswapV3SwapCallback(
        int256 amount0Delta,
        int256 amount1Delta,
        bytes calldata data
    ) external override;

    function executeSwap(
        address pool,
        address recipient,
        bool zeroForOne,
        int256 amountSpecified,
        uint160 sqrtPriceLimitX96
    ) external onlyOwner returns (int256 amount0, int256 amount1);
}
```

## 使用方法

### 1. 命令行测试

```bash
# 测试增强版直接池子 swap
bun run test-enhanced-direct-swap

# 或使用 npm script
npm run test-enhanced-direct-swap
```

### 2. 编程接口

```typescript
import { DirectV3PoolSwapService } from './src/core/DirectV3PoolSwapService';
import { BSC_CONTRACTS } from './src/constants/contracts';

const service = new DirectV3PoolSwapService();
const bankPoolAddress = BSC_CONTRACTS.BNB_BANK_POOL;

// 1. 获取池子状态
const poolState = await service.getPoolState(bankPoolAddress);
console.log('池子状态:', poolState);

// 2. 计算当前价格
const price = await service.calculatePrice(bankPoolAddress);
console.log('当前价格:', price);

// 3. 模拟 swap
const simulation = await service.simulateSwap(
	bankPoolAddress,
	true, // BNB -> BANK
	parseEther('0.01')
);
console.log('模拟结果:', simulation);

// 4. 执行真实 swap
const swapParams = {
	poolAddress: bankPoolAddress,
	recipient: walletAddress,
	zeroForOne: true, // BNB -> BANK
	amountSpecified: parseEther('0.01'),
	sqrtPriceLimitX96: 0n, // 无价格限制
	slippageTolerance: 1, // 1%
};

const result = await service.executeSwap(swapParams);
console.log('Swap 结果:', result);
```

## 参数说明

### DirectSwapParams

```typescript
interface DirectSwapParams {
	poolAddress: Address; // 池子合约地址
	recipient: Address; // 接收代币的地址
	zeroForOne: boolean; // true: token0->token1, false: token1->token0
	amountSpecified: bigint; // 指定金额（正数=精确输入，负数=精确输出）
	sqrtPriceLimitX96: bigint; // 价格限制（0=无限制）
	slippageTolerance: number; // 滑点容忍度（百分比）
}
```

### 交易方向说明

对于 BANK/BNB 池子：

- `token0`: BANK (******************************************)
- `token1`: WBNB (******************************************)

```typescript
// BNB -> BANK
zeroForOne: false; // token1(WBNB) -> token0(BANK)

// BANK -> BNB
zeroForOne: true; // token0(BANK) -> token1(WBNB)
```

## 测试结果示例

```
📊 获取池子信息:
   Token0: ****************************************** (BANK)
   Token1: ****************************************** (WBNB)

💰 计算当前价格:
   sqrtPriceX96: 857487618662941414376498556
   当前价格: 9280601.655098 (BANK/BNB)
   当前 tick: -90527

🔄 模拟 Swap (0.01 BNB -> BANK):
   预估 amount0: -92762.114788633280068053 BANK
   预估 amount1: 0.01 BNB
   价格影响: 0.0000%
```

## 安全注意事项

### ⚠️ 重要警告

1. **测试网络优先**

   - 建议先在 BSC 测试网上验证功能
   - 确认所有参数正确后再在主网使用

2. **回调合约风险**

   - 直接池子调用需要实现回调接口
   - 当前使用简化的回调逻辑
   - 生产环境建议部署专用的回调合约

3. **流动性风险**

   - BANK 代币流动性较低，可能有较大滑点
   - 建议使用小额测试
   - 设置合理的滑点保护

4. **Gas 费用**
   - 直接池子调用可能消耗更多 Gas
   - 建议预留充足的 BNB 作为 Gas 费

### 🔒 最佳实践

1. **授权管理**

   ```typescript
   // 检查当前授权
   const allowance = await service.checkAllowance(
   	tokenAddress,
   	userAddress,
   	poolAddress
   );

   // 只授权必要的金额
   await service.approveToken(tokenAddress, poolAddress, amountNeeded);
   ```

2. **滑点保护**

   ```typescript
   const swapParams = {
   	// ... 其他参数
   	slippageTolerance: 1, // 1% 滑点保护
   	sqrtPriceLimitX96: calculatePriceLimit(currentPrice, slippageTolerance),
   };
   ```

3. **错误处理**
   ```typescript
   try {
   	const result = await service.executeSwap(swapParams);
   	if (result.success) {
   		console.log('Swap 成功:', result.transactionHash);
   	} else {
   		console.error('Swap 失败:', result.error);
   	}
   } catch (error) {
   	console.error('执行错误:', error);
   }
   ```

## 与其他方案的比较

| 方案              | 优点                                                 | 缺点                                | 适用场景                   |
| ----------------- | ---------------------------------------------------- | ----------------------------------- | -------------------------- |
| **直接池子 Swap** | 绕过 Quoter 限制<br/>更直接的控制<br/>可能更低的 Gas | 需要回调实现<br/>更复杂的逻辑       | Quoter 失效时<br/>高级用户 |
| **V3 Router**     | 安全可靠<br/>经过验证<br/>简单易用                   | 依赖 Router 合约<br/>可能有额外费用 | 一般用户<br/>生产环境      |
| **V2 Router**     | 成熟稳定<br/>广泛支持                                | 流动性可能不足<br/>价格可能不优     | 备选方案                   |

## 故障排除

### 常见问题

1. **"钱包未连接"错误**

   - 确保 `.env` 文件中设置了 `PRIVATE_KEY`
   - 检查私钥格式是否正确

2. **"余额不足"错误**

   - 检查钱包中是否有足够的代币
   - 确认代币地址是否正确

3. **"授权失败"错误**

   - 检查网络连接
   - 确认有足够的 BNB 支付 Gas 费

4. **"模拟失败"错误**
   - 检查池子地址是否正确
   - 确认池子是否有足够的流动性

### 调试技巧

1. **启用详细日志**

   ```typescript
   // 在调用前添加
   console.log('调试信息:', {
   	poolAddress,
   	userAddress,
   	tokenBalance,
   	allowance,
   });
   ```

2. **分步测试**

   ```bash
   # 先测试池子状态
   bun run test-bank-pool

   # 再测试直接 swap
   bun run test-enhanced-direct-swap
   ```

## 未来改进

### 计划中的功能

1. **完整的回调合约**

   - 部署专用的 V3SwapCallback 合约
   - 支持更复杂的回调逻辑
   - 增强安全性和可靠性

2. **多池子支持**

   - 支持任意 V3 池子的直接 swap
   - 自动检测池子参数
   - 动态 ABI 加载

3. **高级功能**

   - 闪电贷集成
   - 多跳 swap 支持
   - MEV 保护

4. **用户界面**
   - Web 界面支持
   - 实时价格监控
   - 交易历史记录

## 总结

直接 V3 池子 swap 功能为 BANK 代币等特殊情况提供了有效的解决方案。虽然比使用 Router 更复杂，但在 Quoter 服务不可用时，这是一个可靠的替代方案。

建议用户根据自己的技术水平和风险承受能力选择合适的方案：

- **新手用户**：优先使用 V3 Router 或 V2 Router
- **高级用户**：可以尝试直接池子 swap
- **开发者**：可以基于此功能开发更高级的交易策略
