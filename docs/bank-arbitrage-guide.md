# BANK 代币三角套利指南

## 概述

本指南专门针对 BANK 代币的三角套利配置和优化，基于我们的深度流动性分析和测试结果。

## BANK 代币信息

- **代币符号**: BANK
- **合约地址**: `0x3AeE7602b612de36088F3ffEd8c8f10E86EbF2bF`
- **小数位数**: 18
- **流动性状况**: 较低，需要特殊处理

## USD1 代币信息

- **代币符号**: USD1
- **合约地址**: `0x8d0D000Ee44948FC98c9B98A4FA4921476f08B0d`
- **小数位数**: 18
- **流动性状况**: 较低，需要特殊处理

## 推荐的三角套利路径配置

### 主要路径

```bash
# .env 文件配置
ARBITRAGE_PATHS=[["BNB", "BANK", "USD1", "BNB"]]
```

### 备用路径（流动性更好）

```bash
# 如果主路径流动性不足，可以尝试这些备用路径
ARBITRAGE_PATHS=[
  ["BNB", "BANK", "USD1", "BNB"],
  ["WBNB", "USDT", "BUSD", "WBNB"],
  ["USDT", "USDC", "BUSD", "USDT"]
]
```

### 多跳路径（通过中间代币）

```bash
# 使用中间代币提高成功率
ARBITRAGE_PATHS=[
  ["BNB", "USDT", "BANK", "USD1", "WBNB", "BNB"]
]
```

## 特殊配置参数

### BANK 代币专用配置

```bash
# 降低利润阈值（BANK 代币利润空间较小）
MIN_PROFIT_THRESHOLD_USD=0.1

# 降低最大滑点（提高交易成功率）
MAX_SLIPPAGE_PERCENT=5

# 降低 Gas 价格（减少成本）
GAS_PRICE_GWEI=3

# BANK 代币特殊参数
BANK_MIN_LIQUIDITY_USD=500
BANK_MAX_TRADE_AMOUNT_USD=50
BANK_PREFERRED_INTERMEDIATE_TOKENS=["WBNB", "USDT", "BUSD"]
```

## 流动性分析结果

### 当前发现的问题

1. **BANK -> USD1 直接交易**

   - V2 流动性: ❌ 不可用
   - V3 流动性: ⚠️ 极低
   - 建议: 使用中间代币路由

2. **BNB -> BANK 交易**

   - V2 流动性: ⚠️ 较低
   - V3 流动性: ⚠️ 较低
   - 建议: 使用较小金额测试

3. **USD1 -> BNB 交易**
   - V2 流动性: ⚠️ 较低
   - V3 流动性: ❌ 不可用
   - 建议: 通过 USDT 或 BUSD 路由

### 推荐的改进策略

#### 策略 1: 多跳路径

```
BNB -> USDT -> BANK -> USDT -> USD1 -> USDT -> BNB
```

#### 策略 2: 混合 DEX 版本

- 步骤 1: 使用 V2 (BNB -> USDT)
- 步骤 2: 使用 V3 (USDT -> BANK)
- 步骤 3: 使用 V2 (BANK -> USD1)

#### 策略 3: 动态金额调整

- 起始金额: 0.01 BNB
- 最大金额: 0.1 BNB
- 根据流动性动态调整

## 使用指南

### 1. 配置检查

```bash
# 检查当前配置
npm run check-config
```

### 2. 流动性分析

```bash
# 运行 BANK 代币专用分析
npm run test-bank-arbitrage
```

### 3. 价格测试

```bash
# 测试具体交易对
npm run test-prices BNB/BANK
npm run test-prices BANK/USD1
npm run test-prices USD1/BNB
```

### 4. 综合诊断

```bash
# 运行完整的 DEX 诊断
npm run diagnose-dex
```

### 5. 执行套利检查

```bash
# 单次检查
npm run check-once

# 持续监控
npm run start-monitoring
```

## 风险管理

### 流动性风险

1. **监控指标**

   - 池子深度
   - 24 小时交易量
   - 价格滑点

2. **风险控制**
   - 设置最大交易金额
   - 实时流动性检查
   - 多路径备选方案

### 技术风险

1. **交易失败处理**

   - Gas 费用估算
   - 交易超时处理
   - 重试机制

2. **价格波动**
   - 实时价格更新
   - 滑点保护
   - 止损机制

## 性能优化建议

### 1. 交易时机

- **最佳时间**: UTC 12:00-16:00 (亚洲交易时间)
- **避免时间**: UTC 0:00-4:00 (流动性最低)

### 2. 金额策略

- **测试金额**: 0.001 BNB
- **生产金额**: 0.01-0.05 BNB
- **最大金额**: 0.1 BNB

### 3. 监控频率

- **高频监控**: 每 10 秒 (流动性充足时)
- **低频监控**: 每 60 秒 (流动性不足时)

## 故障排除

### 常见问题

1. **"BANK 代币流动性不足"**

   - 解决方案: 降低交易金额或使用多跳路径

2. **"无法获取有效报价"**

   - 解决方案: 检查网络连接和 RPC 配置

3. **"交易失败"**
   - 解决方案: 增加 Gas 费用或调整滑点设置

### 调试命令

```bash
# V3 池子诊断
npm run diagnose-v3-pool

# 直接 Quoter 测试
npm run test-direct-quoter

# 统一 DEX 服务测试
npm run diagnose-dex
```

## 监控和日志

### 关键指标

1. **成功率**: 目标 > 60%
2. **平均利润**: 目标 > $0.10
3. **执行时间**: 目标 < 30 秒

### 日志分析

- 查看 `logs/arbitrage.log` 了解详细执行情况
- 监控错误模式和频率
- 分析最佳执行时间段

## 未来改进方向

1. **多 DEX 集成**

   - Biswap
   - ApeSwap
   - 1inch

2. **高级策略**

   - 闪电贷套利
   - 跨链套利
   - MEV 保护

3. **自动化优化**
   - 机器学习价格预测
   - 动态路径选择
   - 智能金额调整

## 联系和支持

如果在使用过程中遇到问题，请：

1. 查看日志文件
2. 运行诊断命令
3. 检查网络和配置
4. 参考本指南的故障排除部分

---

**免责声明**: 加密货币交易存在风险，请谨慎操作并做好风险管理。本指南仅供参考，不构成投资建议。
