# 代币符号配置指南

## 概述

现在您可以在配置文件中使用代币符号而不是完整的合约地址，这使得配置更加简洁和易读。同时，重要的合约地址也已经预定义在常量文件中。

## 支持的代币符号

### 主要代币

- `BNB` / `WBNB`: `******************************************`

### 稳定币

- `USDT`: `******************************************`
- `USDC`: `******************************************`
- `BUSD`: `******************************************`
- `DAI`: `******************************************`

### 主流代币

- `BTCB`: `******************************************`
- `ETH`: `******************************************`
- `CAKE`: `******************************************`

### 您的自定义代币

- `BANK`: `******************************************`
- `USD1`: `******************************************`

## 预定义的合约地址

系统已预定义了重要的合约地址常量（在 `src/constants/contracts.ts` 中）：

### PancakeSwap 合约

- **PancakeSwap V2 Router**: `******************************************`
- **PancakeSwap V2 Factory**: `******************************************`
- **PancakeSwap V3 Router**: `******************************************`

### 其他重要合约

- **WBNB**: `******************************************`
- **Chainlink BNB/USD**: `******************************************`

这些地址会自动用作默认值，您无需在 `.env` 文件中重复配置。

## 配置方式

### 方式 1: 使用代币符号 (推荐)

```bash
ARBITRAGE_PATHS=[["BNB", "BANK", "USD1", "BNB"]]
```

### 方式 2: 使用完整地址

```bash
ARBITRAGE_PATHS=[["******************************************", "******************************************", "******************************************", "******************************************"]]
```

### 方式 3: 混合使用

```bash
ARBITRAGE_PATHS=[["BNB", "******************************************", "USD1", "BNB"]]
```

### 多个套利路径

```bash
ARBITRAGE_PATHS=[["BNB", "BANK", "USD1", "BNB"], ["USDT", "BANK", "BNB", "USDT"]]
```

## 您的配置

基于您提供的代币信息，推荐配置：

```bash
# 三角套利路径: BNB -> BANK -> USD1 -> BNB
ARBITRAGE_PATHS=[["BNB", "BANK", "USD1", "BNB"]]
```

## 添加新代币

如需添加新代币符号，请编辑 `src/constants/tokens.ts` 文件：

```typescript
export const BSC_TOKENS = {
	// ... 现有代币

	// 添加新代币
	YOUR_TOKEN: '0x新代币地址',
} as const;
```

## 验证配置

配置完成后，运行以下命令验证：

```bash
# 测试价格获取
npm run test-prices

# 检查套利机会
npm run check-once
```

系统会自动解析代币符号并显示解析结果。
