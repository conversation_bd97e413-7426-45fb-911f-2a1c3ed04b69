const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransport({
  host: 'email-smtp.us-west-2.amazonaws.com', // 替换为你的 SES 区域
  port: 587, // 也可用465（SSL），587推荐（STARTTLS）
  secure: false, // true 用于465端口，587用false
  auth: {},
});

transporter.sendMail(
  {
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Hello from <PERSON>demailer + SES SMTP',
    text: 'This is a test email via SMTP',
  },
  (err, info) => {
    if (err) {
      console.error(err);
    } else {
      console.log(info.envelope);
      console.log(info.messageId);
    }
  }
);
