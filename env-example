# BSC 网络配置
# BSC 主网 RPC URL (通过 Alchemy 或其他提供商)
BSC_RPC_URL=https://bnb-mainnet.g.alchemy.com/v2/YOUR_API_KEY
BSC_WS_RPC_URL=wss://bnb-mainnet.g.alchemy.com/v2/YOUR_API_KEY

# 钱包私钥 (不包含 0x 前缀)
PRIVATE_KEY=your_private_key_without_0x_prefix

# Alchemy API 密钥 (如果单独需要，通常已包含在 BSC_RPC_URL 中)
ALCHEMY_API_KEY=your_alchemy_api_key

# 套利合约地址 (部署后自动填入)
ARBITRAGE_CONTRACT_ADDRESS=

# 套利路径配置 (JSON 格式的字符串数组)
# 示例: [["TOKEN_A_ADDRESS", "TOKEN_B_ADDRESS", "TOKEN_C_ADDRESS", "TOKEN_A_ADDRESS"]]
# 三角套利路径配置 (BNB -> BANK -> USD1 -> BNB)
ARBITRAGE_PATHS=[["BNB", "BANK", "USD1", "BNB"]]

# 套利参数配置
# 最小利润阈值 (美元)
MIN_PROFIT_THRESHOLD_USD=1.0

# Gas 价格 (Gwei)
GAS_PRICE_GWEI=1

# 最大滑点百分比
MAX_SLIPPAGE_PERCENT=0.1