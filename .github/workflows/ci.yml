name: CI
on:
      push:
            branches:
                  - 'main'
      pull_request: {}

jobs:
      ci:
            strategy:
                  matrix:
                        os: [ubuntu-latest, macos-latest, windows-latest]
                        node-version: [20.x]
            runs-on: ${{ matrix.os }}
            steps:
                  - name: Checkout repository
                    uses: actions/checkout@v4

                  - name: Setup Node.js
                    uses: actions/setup-node@v4
                    with:
                          node-version: ${{ matrix.node-version }}

                  - name: Setup Bun
                    uses: oven-sh/setup-bun@v1
                    with:
                          bun-version: latest
                          
                  - name: Install dependencies
                    run: bun install

                  - name: Run validation
                    run: bun run tsc && bun run lint && bun run format

                  - name: Run tests
                    run: bun run test
