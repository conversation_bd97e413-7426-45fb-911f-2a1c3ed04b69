name: Release

permissions:
      contents: write
      id-token: write

on:
      push:
            tags:
                  - 'v*'

jobs:
      release:
            runs-on: ubuntu-latest
            steps:
                  - uses: actions/checkout@v4
                    with:
                          fetch-depth: 0
                  - uses: actions/setup-node@v4
                    with:
                          node-version: lts/*
                          registry-url: https://registry.npmjs.org/
                  - uses: oven-sh/setup-bun@v1
                    with:
                          bun-version: latest
                  - run: bun install
                  - run: bun run build
                  - run: npx changelogithub
                    continue-on-error: true
                    env:
                          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
                  - run: bun run publish:ci
                    env:
                          NPM_CONFIG_TOKEN: ${{ secrets.NPM_TOKEN }}
                          NPM_CONFIG_PROVENANCE: true
