name: Close inactive issues
on:
      schedule:
            - cron: '30 1 * * *'

jobs:
      close-issues:
            runs-on: ubuntu-latest
            permissions:
                  issues: write
                  pull-requests: write
            steps:
                  - uses: actions/stale@v5
                    with:
                          days-before-issue-stale: 30
                          days-before-issue-close: 14
                          stale-issue-label: 'stale'
                          stale-issue-message: 'This issue is stale because it has been open for 30 days with no activity.'
                          close-issue-message: 'This issue was closed because it has been inactive for 14 days since being marked as stale.'
                          days-before-pr-stale: -1
                          days-before-pr-close: -1
                          repo-token: ${{ secrets.GITHUB_TOKEN }}
