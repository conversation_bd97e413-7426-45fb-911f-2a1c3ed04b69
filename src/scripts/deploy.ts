import fs from 'node:fs';
import path from 'node:path';
import solc from 'solc';
import { type Address, encodeDeployData, parseEther } from 'viem';
import { bsc } from 'viem/chains';
import { getWalletClient, publicClient } from '../core/BlockchainService'; // Adjusted path
import { PANCAKESWAP_ROUTER_ADDRESS } from '../utils/config'; // Adjusted path

// 定义编译输出的类型
interface CompilationError {
  severity: string;
  formattedMessage: string;
}

interface CompilationOutput {
  contracts: {
    [fileName: string]: {
      [contractName: string]: {
        abi: unknown[];
        evm: {
          bytecode: {
            object: string;
          };
        };
      };
    };
  };
  errors?: CompilationError[];
}

/**
 * Compile the ArbitrageContract.sol file
 */
async function compileContract(): Promise<CompilationOutput> {
  console.log('🔨 开始编译合约...');

  // Read the contract source code
  // __dirname will be src/scripts, so ../contracts/ is correct
  const contractPath = path.join(__dirname, '../contracts/ArbitrageContract.sol');
  const source = fs.readFileSync(contractPath, 'utf8');

  // Prepare the input for the Solidity compiler
  const input = {
    language: 'Solidity',
    sources: {
      'ArbitrageContract.sol': {
        content: source,
      },
    },
    settings: {
      outputSelection: {
        '*': {
          '*': ['abi', 'evm.bytecode'],
        },
      },
    },
  };

  // Compile the contract
  const output: CompilationOutput = JSON.parse(solc.compile(JSON.stringify(input)));

  // Check for compilation errors
  if (output.errors) {
    const hasErrors = output.errors.some((error: CompilationError) => error.severity === 'error');
    if (hasErrors) {
      console.error('❌ Compilation errors:');
      for (const error of output.errors) {
        if (error.severity === 'error') {
          console.error(`  - ${error.formattedMessage}`);
        }
      }
      throw new Error('Contract compilation failed');
    }
    // Only warnings
    console.warn('⚠️  Compilation warnings:');
    for (const error of output.errors) {
      console.warn(`  - ${error.formattedMessage}`);
    }
  }

  console.log('✅ 合约编译成功');
  return output;
}

/**
 * Deploy the ArbitrageContract to BSC
 */
async function deployContractInternal(): Promise<string> {
  // Renamed to avoid conflict if imported directly
  try {
    // Compile the contract first
    const compilationResult = await compileContract();

    // Get contract artifacts
    const contractArtifact = compilationResult.contracts['ArbitrageContract.sol'].ArbitrageContract;
    const abi = contractArtifact.abi;
    const bytecode = `0x${contractArtifact.evm.bytecode.object}` as `0x${string}`;

    console.log('🚀 开始部署合约...');

    // Prepare constructor arguments
    const constructorArgs = [PANCAKESWAP_ROUTER_ADDRESS] as const;

    // Encode constructor arguments
    const deployData = `${bytecode}` as `0x${string}`;

    console.log('📊 部署参数:');
    console.log(`   Router Address: ${PANCAKESWAP_ROUTER_ADDRESS}`);
    console.log(`   Bytecode Length: ${bytecode.length} characters`);

    // 获取 walletClient（需要私钥）
    const walletClient = getWalletClient();

    // 确保 walletClient.account 存在
    if (!walletClient.account) {
      throw new Error('Wallet account is not available');
    }

    // Estimate gas for deployment transaction
    const gasEstimate = await publicClient.estimateGas({
      account: walletClient.account,
      data: deployData,
      value: BigInt(0),
    });

    console.log(`⛽ 预估 Gas: ${gasEstimate}`);

    // Deploy contract using sendTransaction
    const hash = await walletClient.sendTransaction({
      account: walletClient.account,
      chain: bsc,
      data: deployData,
      gas: gasEstimate + BigInt(50000), // Add some buffer
      value: BigInt(0),
    });

    console.log(`📝 交易哈希: ${hash}`);
    console.log('⏳ 等待交易确认...');

    // Wait for transaction receipt
    const receipt = await publicClient.waitForTransactionReceipt({ hash });

    if (receipt.status === 'success' && receipt.contractAddress) {
      console.log('✅ Contract deployed successfully!');
      console.log(`📍 Contract address: ${receipt.contractAddress}`);
      console.log(`⛽ Gas used: ${receipt.gasUsed}`);

      return receipt.contractAddress;
    }
    throw new Error('Contract deployment failed');
  } catch (error) {
    console.error('❌ Deployment failed:', error);
    throw error;
  }
}

/**
 * Update .env file with deployed contract address
 */
function updateEnvFile(contractAddress: Address): void {
  console.log('📝 Updating .env file...');

  // __dirname will be src/scripts, so ../../.env is correct
  const envPath = path.join(__dirname, '../../.env');
  let envContent = '';

  // Read existing .env file if it exists
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }

  // Update or add ARBITRAGE_CONTRACT_ADDRESS
  const addressLine = `ARBITRAGE_CONTRACT_ADDRESS="${contractAddress}"`;

  if (envContent.includes('ARBITRAGE_CONTRACT_ADDRESS=')) {
    // Replace existing line
    envContent = envContent.replace(/ARBITRAGE_CONTRACT_ADDRESS=.*/, addressLine);
  } else {
    // Add new line
    envContent += `\n${addressLine}\n`;
  }

  // Write back to .env file
  fs.writeFileSync(envPath, envContent);
  console.log('✅ .env file updated with contract address');
}

/**
 * Main deployment function, exported as deployContract
 */
export async function deployContract(): Promise<string> {
  try {
    console.log('🔧 BSC Triangular Arbitrage Contract Deployment');
    console.log('================================================');

    // Deploy contract
    const contractAddress = await deployContractInternal();

    // Update .env file
    updateEnvFile(contractAddress as Address);

    console.log('================================================');
    console.log('🎉 Deployment completed successfully!');
    console.log(`📍 Contract Address: ${contractAddress}`);
    console.log('💡 Next steps:');
    console.log('   1. Verify the contract on BSCScan (optional)');
    console.log('   2. Fund the contract with tokens for arbitrage');
    console.log('   3. Test the arbitrage functionality');
    return contractAddress; // Ensure it returns the address
  } catch (error) {
    console.error('💥 Deployment failed:', error);
    process.exit(1); // Or rethrow error
  }
}

// Run deployment if this script is executed directly (e.g. bun run src/scripts/deploy.ts)
if (require.main === module) {
  deployContract().catch((err) => {
    console.error('Deployment script failed:', err);
    process.exit(1);
  });
}

// Export specific functions if they need to be called individually elsewhere,
// though deployContract is the main entry point.
export { compileContract, updateEnvFile };
// deployContract is already exported above.
