import type { Address } from 'viem';
import type { DexVersion } from '../core/UnifiedDexService';

/**
 * 套利路径类型定义
 */
export interface ArbitragePath {
  /** 代币地址路径 [A, B, C, A] */
  path: Address[];
  /** 路径描述 (可选) */
  description?: string;
}

/**
 * 套利机会信息
 */
export interface ArbitrageOpportunity {
  /** 套利路径 */
  path: Address[];
  /** 输入金额 */
  inputAmount: bigint;
  /** 预期输出金额 */
  outputAmount: bigint;
  /** 实际利润 (扣除费用后) */
  profit: bigint;
  /** 利润率 (百分比) */
  profitPercentage: number;
  /** 预估 Gas 费用 (USD) */
  estimatedGasCostUSD: number;
  /** 预估利润 (USD) */
  estimatedProfitUSD: number;
  /** 是否盈利 */
  profitable: boolean;
  /** 发现时间戳 */
  timestamp: Date;
  /** 价格查询详情 */
  priceQueries: PriceQueryResult[];
}

/**
 * 价格查询结果
 */
export interface PriceQueryResult {
  /** 代币路径 */
  path: Address[];
  /** 输入金额 */
  inputAmount: bigint;
  /** 输出金额 */
  outputAmount: bigint;
  /** DEX 版本 */
  version: DexVersion;
  /** 手续费 (V3 专用) */
  fee?: number;
}

/**
 * 套利配置
 */
export interface ArbitrageConfig {
  /** 套利路径列表 */
  paths: Address[][];
  /** 最小利润阈值 (USD) */
  minProfitThresholdUSD: number;
  /** 最大滑点百分比 */
  maxSlippagePercent: number;
  /** Gas 价格 (Gwei) */
  gasPriceGwei: number;
  /** 检查间隔 (毫秒) */
  checkIntervalMs: number;
}

/**
 * 套利统计信息
 */
export interface ArbitrageStats {
  /** 总检查次数 */
  totalChecks: number;
  /** 发现的机会数量 */
  opportunitiesFound: number;
  /** 成功交易数量 */
  successfulTrades: number;
  /** 失败交易数量 */
  failedTrades: number;
  /** 总利润 (USD) */
  totalProfitUSD: number;
  /** 最后检查时间 */
  lastCheckTime: Date;
  /** 平均检查耗时 (毫秒) */
  averageCheckDuration: number;
}
