/**
 * BSC 主网重要合约地址常量
 * 数据来源: https://developer.pancakeswap.finance/contracts/universal-router/addresses
 */

import type { Address } from 'viem';

export const BSC_CONTRACTS = {
  // PancakeSwap Smart Router V3 (最新版本 - 推荐使用)
  PANCAKESWAP_SMART_ROUTER_V3: '0xd9c500dff816a1da21a48a732d3498bf09dc9aeb',
  PANCAKESWAP_INFINITY_UNIVERSAL_ROUTER: '0xd9c500dff816a1da21a48a732d3498bf09dc9aeb',
  PANCAKESWAP_V3_UNIVERSAL_ROUTER: '0x1A0A18AC4BECDDbd6389559687d1A73d8927E416',

  // PancakeSwap V2 合约 (传统版本)
  PANCAKESWAP_V2_ROUTER: '0x10ED43C718714eb63d5aA57B78B54704E256024E',
  PANCAKESWAP_V2_FACTORY: '0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73',

  // PancakeSwap V3 合约
  PANCAKESWAP_V3_ROUTER: '0x13f4EA83D0bd40E75C8222255bc855a974568Dd4',
  PANCAKESWAP_V3_FACTORY: '0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865',
  PANCAKESWAP_V3_QUOTER: '0xB048Bbc1Ee6b733FFfCFb9e9CeF7375518e25997',

  // 特定交易池地址 (根据文档)
  BNB_BANK_POOL: '0xee6ff918a1f68b5d2fdecb14b367fa2eb5c6951c',
  BNB_USD1_POOL: '0x4a3218606af9b4728a9f187e1c1a8c07fbc172a9',
  USD1_BANK_POOL: '0x461f6989943a0820c12db66bd962d245b587eb3c',

  // Uniswap V2 兼容 Router (备用)
  UNISWAP_V2_ROUTER: '0x10ED43C718714eb63d5aA57B78B54704E256024E', // 与 PancakeSwap V2 相同

  // 其他重要合约
  WBNB: '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c', // WBNB 合约

  // 多签钱包 (如果需要)
  PANCAKESWAP_MULTISIG: '0x1b6C9c20693afDE803B27F8782156c0f892ABC2d',

  // 预言机合约 (如果需要价格数据)
  CHAINLINK_BNB_USD: '0x0567F2323251f0Aab15c8dFb1967E4e8A7D42aeE',
} as const;

/**
 * BSC 测试网合约地址
 */
export const BSC_TESTNET_CONTRACTS = {
  // PancakeSwap Universal Router (测试网)
  PANCAKESWAP_INFINITY_UNIVERSAL_ROUTER: '0x87FD5305E6a40F378da124864B2D479c2028BD86',
  PANCAKESWAP_V3_UNIVERSAL_ROUTER: '0x9A082015c919AD0E47861e5Db9A1c7070E81A2C7',

  // PancakeSwap V2 测试网
  PANCAKESWAP_V2_ROUTER: '0xD99D1c33F9fC3444f8101754aBC46c52416550D1',
  PANCAKESWAP_V2_FACTORY: '0x6725F303b657a9451d8BA641348b6761A6CC7a17',

  // WBNB 测试网
  WBNB: '0xae13d989daC2f0dEbFf460aC112a837C89BAa7cd',
} as const;

/**
 * 合约名称类型
 */
export type ContractName = keyof typeof BSC_CONTRACTS;

/**
 * 根据合约名称获取地址
 */
export function getContractAddress(contractName: ContractName): string {
  const address = BSC_CONTRACTS[contractName];
  if (!address) {
    throw new Error(`未找到合约 ${contractName} 的地址`);
  }
  return address;
}

/**
 * 验证合约名称是否存在
 */
export function isValidContractName(name: string): name is ContractName {
  return name in BSC_CONTRACTS;
}

/**
 * 获取所有支持的合约名称
 */
export function getAllContractNames(): ContractName[] {
  return Object.keys(BSC_CONTRACTS) as ContractName[];
}

/**
 * 根据地址获取合约名称
 */
export function getContractName(address: string): ContractName | undefined {
  const normalizedAddress = address.toLowerCase();
  for (const [name, addr] of Object.entries(BSC_CONTRACTS)) {
    if (addr.toLowerCase() === normalizedAddress) {
      return name as ContractName;
    }
  }
  return undefined;
}

/**
 * 常用合约地址快捷访问
 */
export const CONTRACTS: {
  readonly PANCAKESWAP_ROUTER: string;
  readonly PANCAKESWAP_SMART_ROUTER_V3: string;
  readonly PANCAKESWAP_UNIVERSAL_ROUTER: string;
  readonly PANCAKESWAP_V3_QUOTER: string;
  readonly WBNB: string;
} = {
  // PancakeSwap Router (V2 - 向后兼容)
  PANCAKESWAP_ROUTER: BSC_CONTRACTS.PANCAKESWAP_V2_ROUTER,

  // PancakeSwap Smart Router V3 (最新版本 - 推荐用于 BANK 代币)
  PANCAKESWAP_SMART_ROUTER_V3: BSC_CONTRACTS.PANCAKESWAP_SMART_ROUTER_V3,

  // PancakeSwap Universal Router (通用版本)
  PANCAKESWAP_UNIVERSAL_ROUTER: BSC_CONTRACTS.PANCAKESWAP_INFINITY_UNIVERSAL_ROUTER,

  // PancakeSwap V3 Quoter (用于价格查询)
  PANCAKESWAP_V3_QUOTER: BSC_CONTRACTS.PANCAKESWAP_V3_QUOTER,

  // WBNB 合约
  WBNB: BSC_CONTRACTS.WBNB,
} as const;

export const PANCAKESWAP_V3_QUOTER_ADDRESS = '0xB048Bbc1Ee6b733FFfCFb9e9CeF7375518e25997' as Address; // Quoter (旧)
export const PANCAKESWAP_V3_QUOTER_V2_ADDRESS = '0x382Fc0828eBB21416F0374d135f8b396574A9670' as Address; // QuoterV2 (新，推荐)

export const PANCAKESWAP_UNIVERSAL_ROUTER_ADDRESS = '0xd9c500dff816a1da21a48a732d3498bf09dc9aeb' as Address;
