/**
 * BSC 主网代币地址常量
 */

import type { Address } from 'viem';

export interface TokenInfo {
  readonly symbol: string;
  readonly address: Address;
  readonly decimals: number;
  readonly name: string; // Optional: For more descriptive logging or UI
}

// Using a more structured approach for token definitions
export const BSC_TOKEN_LIST_DEFINITIONS: readonly TokenInfo[] = [
  // Major Tokens
  { symbol: 'WBNB', address: '******************************************', decimals: 18, name: 'Wrapped BNB' },
  { symbol: 'BNB', address: '******************************************', decimals: 18, name: 'Binance Coin' }, // Alias for WBNB

  // Stablecoins
  { symbol: 'USDT', address: '******************************************', decimals: 18, name: 'Tether USD' },
  { symbol: 'USDC', address: '******************************************', decimals: 18, name: 'USD Coin' },
  { symbol: 'BUSD', address: '******************************************', decimals: 18, name: 'Binance USD' }, // Note: BUSD is being phased out
  { symbol: 'DAI', address: '******************************************', decimals: 18, name: 'Dai Stablecoin' },

  // Mainstream Tokens
  { symbol: 'BTCB', address: '******************************************', decimals: 18, name: 'Bitcoin BEP2' },
  { symbol: 'ETH', address: '******************************************', decimals: 18, name: 'Ethereum BEP20' },
  { symbol: 'CAKE', address: '******************************************', decimals: 18, name: 'PancakeSwap Token' },

  // Custom Tokens (User-provided or specific to the project)
  { symbol: 'BANK', address: '******************************************', decimals: 18, name: 'BANK Token' }, // Assuming 18 decimals
  { symbol: 'USD1', address: '******************************************', decimals: 18, name: 'USD1 Token' }, // Assuming 18 decimals
] as const;

/**
 * Token symbol type, derived from the actual token symbols
 */
export type TokenSymbol = (typeof BSC_TOKEN_LIST_DEFINITIONS)[number]['symbol'];

// For quick lookup by symbol
export const BSC_TOKENS: { readonly [K in TokenSymbol]: TokenInfo } = Object.fromEntries(
  BSC_TOKEN_LIST_DEFINITIONS.map((token) => [token.symbol, token]),
) as { readonly [K in TokenSymbol]: TokenInfo };

/**
 * Retrieves the full TokenInfo object for a given symbol.
 */
export function getTokenInfo(symbol: TokenSymbol): TokenInfo {
  const tokenInfo = BSC_TOKENS[symbol];
  if (!tokenInfo) {
    throw new Error(`Token information not found for symbol: ${symbol}`);
  }
  return tokenInfo;
}

/**
 * Retrieves the address for a given token symbol.
 */
export function getTokenAddress(symbol: TokenSymbol): Address {
  return getTokenInfo(symbol).address;
}

/**
 * Retrieves the decimals for a given token symbol.
 */
export function getTokenDecimals(symbol: TokenSymbol): number {
  return getTokenInfo(symbol).decimals;
}

/**
 * Validates if a string is a known token symbol.
 */
export function isValidTokenSymbol(symbol: string): symbol is TokenSymbol {
  return Object.keys(BSC_TOKENS).includes(symbol);
}

/**
 * Returns an array of all known token symbols.
 */
export function getAllTokenSymbols(): TokenSymbol[] {
  return Object.keys(BSC_TOKENS) as TokenSymbol[];
}

/**
 * Retrieves the token symbol for a given address.
 * Returns undefined if no token matches the address.
 */
export function getTokenSymbolByAddress(address: string): TokenSymbol | undefined {
  const normalizedAddress = address.toLowerCase();
  const foundEntry = Object.entries(BSC_TOKENS).find(
    ([, tokenInfo]) => tokenInfo.address.toLowerCase() === normalizedAddress,
  );
  return foundEntry ? (foundEntry[0] as TokenSymbol) : undefined;
}

/**
 * Retrieves TokenInfo for a given address.
 * Returns undefined if no token matches the address.
 */
export function getTokenInfoByAddress(address: string): TokenInfo | undefined {
  const symbol = getTokenSymbolByAddress(address);
  return symbol ? BSC_TOKENS[symbol] : undefined;
}
