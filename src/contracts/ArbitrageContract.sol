// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "./interfaces/IPancakeRouter02.sol";

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function transferFrom(address from, address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

contract ArbitrageContract {
    address private owner;
    IPancakeRouter02 public immutable pancakeRouter;
    
    event ArbitrageExecuted(
        address indexed tokenA,
        uint256 amountIn,
        uint256 amountOut,
        uint256 profit
    );
    
    event ArbitrageFailed(
        address indexed tokenA,
        uint256 amountIn,
        string reason
    );

    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }

    constructor(address _pancakeRouter) {
        owner = msg.sender;
        pancakeRouter = IPancakeRouter02(_pancakeRouter);
    }

    /**
     * @dev Execute triangular arbitrage
     * @param _path Array of token addresses for the arbitrage path (must be length 4: A->B->C->A)
     * @param _amountIn Amount of token A to start with
     * @param _minAmountOut Minimum amount of token A expected at the end (slippage protection)
     */
    function executeArbitrage(
        address[] memory _path,
        uint256 _amountIn,
        uint256 _minAmountOut
    ) external onlyOwner {
        require(_path.length == 4, "Path must have exactly 4 addresses for triangular arbitrage");
        require(_path[0] == _path[3], "First and last tokens must be the same");
        require(_amountIn > 0, "Amount in must be greater than 0");

        address tokenA = _path[0];
        IERC20 tokenAContract = IERC20(tokenA);
        
        // Check initial balance
        uint256 initialBalance = tokenAContract.balanceOf(address(this));
        require(initialBalance >= _amountIn, "Insufficient token A balance in contract");

        try this._executeArbitrageInternal(_path, _amountIn) {
            // Check final balance
            uint256 finalBalance = tokenAContract.balanceOf(address(this));
            require(finalBalance >= initialBalance - _amountIn + _minAmountOut, 
                    "Arbitrage did not meet minimum profit requirement");
            
            uint256 profit = finalBalance - (initialBalance - _amountIn);
            emit ArbitrageExecuted(tokenA, _amountIn, finalBalance - initialBalance + _amountIn, profit);
            
        } catch Error(string memory reason) {
            emit ArbitrageFailed(tokenA, _amountIn, reason);
            revert(string(abi.encodePacked("Arbitrage failed: ", reason)));
        } catch {
            emit ArbitrageFailed(tokenA, _amountIn, "Unknown error");
            revert("Arbitrage failed with unknown error");
        }
    }

    /**
     * @dev Internal function to execute the arbitrage swaps
     * @param _path Token path for arbitrage
     * @param _amountIn Initial amount
     */
    function _executeArbitrageInternal(
        address[] memory _path,
        uint256 _amountIn
    ) external {
        require(msg.sender == address(this), "Only callable internally");
        
        // Step 1: A -> B
        address[] memory pathAB = new address[](2);
        pathAB[0] = _path[0]; // Token A
        pathAB[1] = _path[1]; // Token B
        
        IERC20(_path[0]).approve(address(pancakeRouter), _amountIn);
        uint256[] memory amountsAB = pancakeRouter.swapExactTokensForTokens(
            _amountIn,
            0, // Accept any amount of Token B
            pathAB,
            address(this),
            block.timestamp + 300 // 5 minutes deadline
        );

        // Step 2: B -> C
        address[] memory pathBC = new address[](2);
        pathBC[0] = _path[1]; // Token B
        pathBC[1] = _path[2]; // Token C
        
        uint256 amountB = amountsAB[1];
        IERC20(_path[1]).approve(address(pancakeRouter), amountB);
        uint256[] memory amountsBC = pancakeRouter.swapExactTokensForTokens(
            amountB,
            0, // Accept any amount of Token C
            pathBC,
            address(this),
            block.timestamp + 300
        );

        // Step 3: C -> A
        address[] memory pathCA = new address[](2);
        pathCA[0] = _path[2]; // Token C
        pathCA[1] = _path[3]; // Token A (same as _path[0])
        
        uint256 amountC = amountsBC[1];
        IERC20(_path[2]).approve(address(pancakeRouter), amountC);
        pancakeRouter.swapExactTokensForTokens(
            amountC,
            0, // Accept any amount of Token A
            pathCA,
            address(this),
            block.timestamp + 300
        );
    }

    /**
     * @dev Withdraw tokens from the contract (only owner)
     * @param _token Token address to withdraw
     * @param _amount Amount to withdraw
     */
    function withdrawToken(address _token, uint256 _amount) external onlyOwner {
        IERC20(_token).transfer(owner, _amount);
    }

    /**
     * @dev Withdraw all tokens of a specific type (only owner)
     * @param _token Token address to withdraw
     */
    function withdrawAllTokens(address _token) external onlyOwner {
        uint256 balance = IERC20(_token).balanceOf(address(this));
        if (balance > 0) {
            IERC20(_token).transfer(owner, balance);
        }
    }

    /**
     * @dev Emergency function to withdraw ETH/BNB (only owner)
     */
    function withdrawETH() external onlyOwner {
        payable(owner).transfer(address(this).balance);
    }

    /**
     * @dev Transfer ownership (only current owner)
     * @param _newOwner New owner address
     */
    function transferOwnership(address _newOwner) external onlyOwner {
        require(_newOwner != address(0), "New owner cannot be zero address");
        owner = _newOwner;
    }

    /**
     * @dev Get current owner
     */
    function getOwner() external view returns (address) {
        return owner;
    }

    // Allow contract to receive ETH/BNB
    receive() external payable {}
} 