// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function transferFrom(address from, address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
}

interface IUniswapV3SwapCallback {
    /// @notice Called to `msg.sender` after executing a swap via IUniswapV3Pool#swap.
    /// @dev In the implementation you must pay the pool tokens owed for the swap.
    /// The caller of this method must be checked to be a UniswapV3Pool deployed by the canonical UniswapV3Factory.
    /// amount0Delta and amount1Delta can both be 0 if no tokens were swapped.
    /// @param amount0Delta The amount of token0 that was sent (negative) or must be received (positive) by the pool by
    /// the end of the swap. If positive, the callback must send that amount of token0 to the pool.
    /// @param amount1Delta The amount of token1 that was sent (negative) or must be received (positive) by the pool by
    /// the end of the swap. If positive, the callback must send that amount of token1 to the pool.
    /// @param data Any data passed through by the caller via the IUniswapV3PoolActions#swap call
    function uniswapV3SwapCallback(
        int256 amount0Delta,
        int256 amount1Delta,
        bytes calldata data
    ) external;
}

/**
 * @title V3SwapCallback
 * @notice 处理 PancakeSwap V3 池子直接 swap 的回调逻辑
 */
contract V3SwapCallback is IUniswapV3SwapCallback {
    address private owner;
    
    event SwapCallback(
        address indexed pool,
        int256 amount0Delta,
        int256 amount1Delta,
        address payer
    );
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    constructor() {
        owner = msg.sender;
    }
    
    /**
     * @notice Uniswap V3 swap 回调函数
     * @param amount0Delta token0 的变化量（正数表示需要支付给池子）
     * @param amount1Delta token1 的变化量（正数表示需要支付给池子）
     * @param data 调用时传递的数据（包含付款人地址）
     */
    function uniswapV3SwapCallback(
        int256 amount0Delta,
        int256 amount1Delta,
        bytes calldata data
    ) external override {
        // 解码数据获取付款人地址
        address payer = abi.decode(data, (address));
        
        // 获取池子的 token0 和 token1 地址
        address token0 = IUniswapV3Pool(msg.sender).token0();
        address token1 = IUniswapV3Pool(msg.sender).token1();
        
        // 如果 amount0Delta > 0，需要向池子支付 token0
        if (amount0Delta > 0) {
            IERC20(token0).transferFrom(payer, msg.sender, uint256(amount0Delta));
        }
        
        // 如果 amount1Delta > 0，需要向池子支付 token1
        if (amount1Delta > 0) {
            IERC20(token1).transferFrom(payer, msg.sender, uint256(amount1Delta));
        }
        
        emit SwapCallback(msg.sender, amount0Delta, amount1Delta, payer);
    }
    
    /**
     * @notice 执行 V3 池子 swap
     * @param pool 池子地址
     * @param recipient 接收代币的地址
     * @param zeroForOne 交易方向
     * @param amountSpecified 指定金额
     * @param sqrtPriceLimitX96 价格限制
     */
    function executeSwap(
        address pool,
        address recipient,
        bool zeroForOne,
        int256 amountSpecified,
        uint160 sqrtPriceLimitX96
    ) external onlyOwner returns (int256 amount0, int256 amount1) {
        // 编码回调数据
        bytes memory data = abi.encode(msg.sender);
        
        // 调用池子的 swap 函数
        (amount0, amount1) = IUniswapV3Pool(pool).swap(
            recipient,
            zeroForOne,
            amountSpecified,
            sqrtPriceLimitX96,
            data
        );
    }
    
    /**
     * @notice 紧急提取代币
     * @param token 代币地址
     * @param amount 提取金额
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner, amount);
    }
    
    /**
     * @notice 转移合约所有权
     * @param newOwner 新所有者地址
     */
    function transferOwnership(address newOwner) external onlyOwner {
        require(newOwner != address(0), "New owner cannot be zero address");
        owner = newOwner;
    }
}

interface IUniswapV3Pool {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function swap(
        address recipient,
        bool zeroForOne,
        int256 amountSpecified,
        uint160 sqrtPriceLimitX96,
        bytes calldata data
    ) external returns (int256 amount0, int256 amount1);
} 