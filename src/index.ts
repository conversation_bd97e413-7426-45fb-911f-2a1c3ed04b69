import { Command } from 'commander';
import dotenv from 'dotenv';
import type { Address } from 'viem';
import {
  type TokenSymbol,
  getTokenAddress,
  getTokenDecimals,
  getTokenInfoByAddress,
  isValidTokenSymbol,
} from './constants/tokens';
import { ArbitrageFinder } from './core/ArbitrageFinder';
import { testBNBBankUSD1Arbitrage } from './core/BNBBankUSD1ArbitrageService';
import { testBankPoolService } from './core/BankPoolService';
import { checkAllPairs, checkYourLPs, diagnosePaths, getAmountsOut } from './core/DexService';
import { testV3Prices } from './core/DexServiceV3';
import { testDirectQuoter } from './core/DirectQuoterService';
import {
  diagnosePoolTokenOrder,
  diagnoseUSD1BankPool,
  testDirectV3PoolSwap,
  testUSD1BankPoolSwap,
  validatePriceCalculations,
} from './core/DirectV3PoolSwapService';
import { testBankArbitragePath } from './core/EnhancedLiquidityService';
import { testPancakeV3Router } from './core/PancakeV3RouterService';
import { diagnoseRouters as testRouterDiagnostics } from './core/RouterDiagnostic';
import { testSmartRouterV3, testSmartRouterV3TriangularPath } from './core/SmartRouterV3Service';
import { superOptimizedArbitrageFinder } from './core/SuperOptimizedArbitrageFinder';
import { testUSD1BankTradingService } from './core/USD1BankTradingService';
import { testUnifiedDexService } from './core/UnifiedDexService';
import { testV3PoolDiagnostics } from './core/V3PoolDiagnosticService';
import { deployContract } from './scripts/deploy';
import {
  ARBITRAGE_CONTRACT_ADDRESS,
  ARBITRAGE_PATHS,
  BSC_RPC_URL,
  MIN_PROFIT_THRESHOLD_USD,
  PANCAKESWAP_ROUTER_ADDRESS,
  PRIVATE_KEY,
} from './utils/config';

dotenv.config();

const program = new Command();

program.name('bsc-triangular-arbitrage').description('CLI for BSC Triangular Arbitrage Bot').version('0.1.0');

program
  .command('check-config')
  .description('检查当前的 .env 配置')
  .action(() => {
    console.log('当前配置:');
    console.log(`BSC_RPC_URL: ${BSC_RPC_URL ? '已设置' : '未设置'}`);
    console.log(`PRIVATE_KEY: ${PRIVATE_KEY ? '已设置' : '未设置'}`);
    console.log(`PANCAKESWAP_ROUTER_ADDRESS: ${PANCAKESWAP_ROUTER_ADDRESS}`);
    console.log(`ARBITRAGE_CONTRACT_ADDRESS: ${ARBITRAGE_CONTRACT_ADDRESS || '未部署或未设置'}`);
    console.log(`MIN_PROFIT_THRESHOLD_USD: ${MIN_PROFIT_THRESHOLD_USD}`);
    console.log('ARBITRAGE_PATHS:');
    ARBITRAGE_PATHS.forEach((path: Address[], index) => {
      const pathDisplay = path
        .map((addr) => {
          const tokenInfo = getTokenInfoByAddress(addr);
          return tokenInfo ? tokenInfo.symbol : addr;
        })
        .join(' -> ');
      console.log(`  路径 ${index + 1}: ${pathDisplay}`);
    });
  });

program
  .command('check-once')
  .description('执行一次套利机会检查')
  .action(async () => {
    console.log('执行一次套利机会检查...');
    const finder = new ArbitrageFinder();
    const opportunities = await finder.checkOnce();
    console.log(`\n✅ 检查完成，发现 ${opportunities.length} 个套利机会`);
  });

program
  .command('check-once-enhanced')
  .description('执行增强版单次套利检查')
  .action(async () => {
    console.log('🔍 执行增强版单次套利检查...');
    const enhancedFinder = new ArbitrageFinder();
    const enhancedOpportunities = await enhancedFinder.checkOnceEnhanced();
    console.log(`\n✅ 增强版检查完成，发现 ${enhancedOpportunities.length} 个套利机会`);
  });

program
  .command('check-once-parallel')
  .description('执行并行化增强版单次套利检查')
  .action(async () => {
    console.log('🚀 执行并行化增强版单次套利检查...');
    const parallelFinder = new ArbitrageFinder();
    const parallelOpportunities = await parallelFinder.checkOnceParallel();
    console.log(`\n✅ 并行化检查完成，发现 ${parallelOpportunities.length} 个套利机会`);
  });

program
  .command('check-once-super-parallel')
  .description('执行超级并行化套利检查（集成所有优化）')
  .action(async () => {
    console.log('🚀 执行超级并行化套利检查...');
    const superParallelFinder = new ArbitrageFinder();
    const superParallelOpportunities = await superParallelFinder.checkOnceSuperParallel();
    console.log(`\n✅ 超级并行化检查完成，发现 ${superParallelOpportunities.length} 个套利机会`);
  });

program
  .command('check-once-ultra-optimized')
  .description('执行超级优化套利检查（Multicall3，最少RPC调用）')
  .action(async () => {
    console.log('🚀 执行超级优化套利检查（Multicall3）...');
    const ultraOptimizedFinder = new ArbitrageFinder();
    const ultraOptimizedOpportunities = await ultraOptimizedFinder.checkOnceUltraOptimized();
    console.log(`\n✅ 超级优化检查完成，发现 ${ultraOptimizedOpportunities.length} 个套利机会`);
  });

program
  .command('start-monitoring')
  .description('开始持续监控套利机会')
  .action(async () => {
    console.log('开始持续监控套利机会...');
    const finder = new ArbitrageFinder();
    await finder.startMonitoring();
  });

program
  .command('deploy-contract')
  .description('编译并部署套利合约')
  .action(async () => {
    console.log('正在部署套利合约...');
    try {
      const contractAddress = await deployContract();
      console.log(`✅ 套利合约部署成功，地址: ${contractAddress}`);
      console.log('请将此地址更新到您的 .env 文件的 ARBITRAGE_CONTRACT_ADDRESS 变量中。');
    } catch (error) {
      console.error('❌ 合约部署失败:', error);
    }
  });

program
  .command('test-prices <pair_or_path>')
  .description('测试特定交易对或路径的价格查询 (例如, USDT/BUSD 或 WBNB/USDT/BUSD)')
  .action(async (pairOrPath: string) => {
    const tokens = pairOrPath.split('/').map((s) => s.trim().toUpperCase());

    if (tokens.length < 2) {
      console.error('错误: 请提供至少两个代币符号，格式为 TOKEN_A/TOKEN_B 或 TOKEN_A/TOKEN_B/TOKEN_C');
      return;
    }

    const pathSymbols = tokens
      .map((t) => {
        if (!isValidTokenSymbol(t)) {
          console.error(`错误: 未知的代币符号 ${t}`);
          return null;
        }
        return t as TokenSymbol;
      })
      .filter(Boolean) as TokenSymbol[];

    if (pathSymbols.length !== tokens.length) {
      return; // Error already logged
    }

    const pathAddresses = pathSymbols.map((s) => getTokenAddress(s));
    const amountIn = 10n ** BigInt(getTokenDecimals(pathSymbols[0])); // 1 unit of the first token

    console.log(`\n🔍 测试价格查询 for ${pairOrPath} using 1 ${pathSymbols[0]}`);
    console.log(`   路径 (地址): ${pathAddresses.join(' -> ')}`);
    console.log(`   输入金额: 1 ${pathSymbols[0]}`);

    try {
      const amountsOut = await getAmountsOut(amountIn, pathAddresses);
      console.log(`   查询结果 (amountsOut): ${amountsOut}`);
      if (amountsOut && amountsOut.length > 1) {
        const amountOutToken = amountsOut[amountsOut.length - 1];
        const lastTokenSymbol = pathSymbols[pathSymbols.length - 1];
        const lastTokenDecimals = getTokenDecimals(lastTokenSymbol);
        const formattedAmountOut = Number(amountOutToken) / 10 ** lastTokenDecimals;
        console.log(`   💰 1 ${pathSymbols[0]} = ${formattedAmountOut.toFixed(6)} ${lastTokenSymbol}`);
      }
    } catch (error) {
      console.error('   价格查询失败:', error);
    }

    console.log('\n--- 旧版价格测试 (USDT -> BUSD) ---');
    const USDT_ADDRESS = getTokenAddress('USDT');
    const BUSD_ADDRESS = getTokenAddress('BUSD');
    const path = [USDT_ADDRESS, BUSD_ADDRESS];
    const testAmountIn = 1000000000000000000n; // 1 USDT (assuming 18 decimals)
    const amounts = await getAmountsOut(testAmountIn, path);
    if (amounts && amounts.length > 1) {
      console.log(`1 USDT = ${Number(amounts[1]) / 10 ** 18} BUSD (使用 ${PANCAKESWAP_ROUTER_ADDRESS})`);
    } else {
      console.log('USDT -> BUSD 价格查询失败');
    }
  });

program
  .command('diagnose-dex')
  .description('运行 PancakeSwap V2 和 V3 的各种诊断功能')
  .action(async () => {
    console.log('\n🔬 运行 PancakeSwap V2 诊断...');
    await diagnosePaths();
    await checkAllPairs();
    await checkYourLPs();
    // await checkLPInfo('YOUR_LP_ADDRESS_HERE' as Address);

    console.log('\n🔬 运行 PancakeSwap V3 (Quoter) 诊断...');
    await testV3Prices();

    console.log('\n🔬 运行 PancakeSwap V3 (Router - 已弃用，仅供参考) 诊断...');
    console.log('(V3 Router test skipped as it was likely based on incorrect assumptions or not exported)');

    console.log('\n🔬 运行 Unified DEX Service 诊断...');
    await testUnifiedDexService();

    console.log('\n🔬 运行 Router/Quoter 综合诊断...');
    await testRouterDiagnostics();

    console.log('\n✅ 所有诊断完成');
  });

program
  .command('test-smart-router-v3')
  .description('测试 PancakeSwap Smart Router V3 (直接调用模拟)')
  .action(async () => {
    await testSmartRouterV3();
    await testSmartRouterV3TriangularPath();
  });

program
  .command('diagnose-v3-pool')
  .description('诊断特定的 PancakeSwap V3 流动性池状态')
  .action(async () => {
    console.log('\n🔬 诊断 PancakeSwap V3 流动性池...');
    await testV3PoolDiagnostics();
  });

program
  .command('test-direct-quoter')
  .description('直接测试 PancakeSwap V3 QuoterV2 的 quoteExactInputSingle 功能')
  .action(async () => {
    await testDirectQuoter();
  });

program
  .command('test-bank-arbitrage')
  .description('测试 BANK 代币的三角套利路径分析和流动性检测')
  .action(async () => {
    await testBankArbitragePath();
  });

program
  .command('test-bank-pool')
  .description('测试 BNB/BANK 交易对的实时兑换比例和池子状态')
  .action(async () => {
    await testBankPoolService();
  });

program
  .command('test-enhanced-direct-swap')
  .description('测试增强版直接 V3 池子 swap 功能（使用完整 ABI）')
  .action(async () => {
    await testDirectV3PoolSwap();
  });

program
  .command('test-v3-router')
  .description('测试 PancakeSwap V3 Router 进行 BANK 代币 swap')
  .action(async () => {
    await testPancakeV3Router();
  });

program
  .command('test-usd1-bank-pool')
  .description('测试 USD1/BANK 池子的直接 swap 功能')
  .action(async () => {
    await testUSD1BankPoolSwap();
  });

program
  .command('diagnose-usd1-bank-pool')
  .description('深度诊断 USD1/BANK 池子的价格和精度问题')
  .action(async () => {
    await diagnoseUSD1BankPool();
  });

program
  .command('test-usd1-bank-trading')
  .description('测试 USD1/BANK 专用交易服务（基于真实交易数据修正）')
  .action(async () => {
    await testUSD1BankTradingService();
  });

program
  .command('test-bnb-bank-usd1')
  .description('测试 BNB-BANK-USD1 三角套利路径分析和流动性检测')
  .action(async () => {
    console.log('🧪 测试 BNB-BANK-USD1 三角套利...');
    await testBNBBankUSD1Arbitrage();
  });

program
  .command('diagnose-pool-token-order')
  .description('诊断三角套利路径中各个池子的 token 顺序和价格信息')
  .action(async () => {
    await diagnosePoolTokenOrder();
  });

program
  .command('validate-price-calculations')
  .description('验证价格计算的准确性，对比计算结果与实际汇率')
  .action(async () => {
    await validatePriceCalculations();
  });

program
  .command('benchmark-performance')
  .description('性能基准测试：对比HTTP vs WebSocket vs Multicall的性能差异')
  .action(async () => {
    console.log('🏁 开始性能基准测试...');
    const finder = new ArbitrageFinder();

    console.log('\n📊 测试1: 并行化版本 (HTTP)');
    const httpStart = Date.now();
    try {
      await finder.checkOnceParallel();
      const httpDuration = Date.now() - httpStart;
      console.log(`⏱️ HTTP版本耗时: ${httpDuration}ms`);
    } catch (error) {
      console.error('HTTP版本测试失败:', error);
    }

    console.log('\n📊 测试2: 超级优化版本 (Multicall3)');
    const multicallStart = Date.now();
    try {
      await finder.checkOnceUltraOptimized();
      const multicallDuration = Date.now() - multicallStart;
      console.log(`⏱️ Multicall版本耗时: ${multicallDuration}ms`);
    } catch (error) {
      console.error('Multicall版本测试失败:', error);
    }

    console.log('\n🏆 性能基准测试完成');
  });

program
  .command('check-once-websocket')
  .description('执行WebSocket版本套利检查（使用WebSocket连接，减少延迟）')
  .action(async () => {
    console.log('🚀 执行WebSocket版本套利检查...');
    console.log('💡 注意: WebSocket版本正在开发中，当前使用Multicall3版本');
    const wsFinder = new ArbitrageFinder();
    try {
      const wsOpportunities = await wsFinder.checkOnceUltraOptimized();
      console.log(`\n✅ WebSocket检查完成，发现 ${wsOpportunities.length} 个套利机会`);
    } catch (error) {
      console.error('❌ WebSocket检查失败:', error);
    }
  });

program
  .command('check-once-super-optimized-v2')
  .description('执行超级优化套利检查V2（集成WebSocket + 智能缓存 + 优化计算）') // 命令处理器已存在
  .action(async () => {
    console.log('🚀 执行超级优化套利检查V2...');
    try {
      const superOpportunities = await superOptimizedArbitrageFinder.checkOnceSuperOptimized();
      console.log(`\n✅ 超级优化V2检查完成，发现 ${superOpportunities.length} 个套利机会`);

      // 显示详细统计
      const stats = superOptimizedArbitrageFinder.getStats();
      console.log('\n📊 详细统计:');
      console.log(`   - 缓存命中率: ${(stats.cacheStats.total.hitRate * 100).toFixed(1)}%`);
      console.log(`   - 订阅池子数: ${stats.subscriptionStats.totalSubscriptions}`);
      console.log(`   - 内存使用: ${(stats.cacheStats.total.memoryUsage / 1024).toFixed(1)}KB`);
    } catch (error) {
      console.error('❌ 超级优化V2检查失败:', error);
    }
  });

program
  .command('start-event-driven-monitoring')
  .description('启动事件驱动的实时套利监控（WebSocket + 智能缓存）')
  .action(async () => {
    console.log('🚀 启动事件驱动套利监控...');
    try {
      await superOptimizedArbitrageFinder.startEventDrivenMonitoring();

      // 保持程序运行
      console.log('✅ 事件驱动监控已启动，按 Ctrl+C 停止...');

      // 优雅关闭处理
      process.on('SIGINT', async () => {
        console.log('\n🛑 收到停止信号，正在清理资源...');
        await superOptimizedArbitrageFinder.cleanup();
        process.exit(0);
      });

      // 定期显示统计信息
      setInterval(() => {
        const stats = superOptimizedArbitrageFinder.getStats();
        console.log(`\n📊 监控统计 (${new Date().toLocaleTimeString()}):`);
        console.log(`   - 总检查次数: ${stats.totalChecks}`);
        console.log(`   - 发现机会: ${stats.opportunitiesFound}`);
        console.log(`   - 缓存命中率: ${(stats.cacheStats.total.hitRate * 100).toFixed(1)}%`);
        console.log(`   - 订阅池子: ${stats.subscriptionStats.totalSubscriptions} 个`);
      }, 30000); // 每30秒显示一次统计
    } catch (error) {
      console.error('❌ 事件驱动监控启动失败:', error);
      process.exit(1);
    }
  });

program
  .command('benchmark-performance-v2')
  .description('性能基准测试V2：对比所有优化版本的性能差异')
  .action(async () => {
    console.log('🏁 开始性能基准测试V2...');
    const finder = new ArbitrageFinder();

    console.log('\n📊 测试1: 并行化版本 (HTTP)');
    const httpStart = Date.now();
    try {
      await finder.checkOnceParallel();
      const httpDuration = Date.now() - httpStart;
      console.log(`⏱️ HTTP版本耗时: ${httpDuration}ms`);
    } catch (error) {
      console.error('HTTP版本测试失败:', error);
    }

    console.log('\n📊 测试2: 超级优化版本 (Multicall3)');
    const multicallStart = Date.now();
    try {
      await finder.checkOnceUltraOptimized();
      const multicallDuration = Date.now() - multicallStart;
      console.log(`⏱️ Multicall版本耗时: ${multicallDuration}ms`);
    } catch (error) {
      console.error('Multicall版本测试失败:', error);
    }

    console.log('\n📊 测试3: 超级优化V2版本 (WebSocket + 智能缓存)');
    const superV2Start = Date.now();
    try {
      await superOptimizedArbitrageFinder.checkOnceSuperOptimized();
      const superV2Duration = Date.now() - superV2Start;
      console.log(`⏱️ 超级优化V2版本耗时: ${superV2Duration}ms`);

      // 显示缓存统计
      const stats = superOptimizedArbitrageFinder.getStats();
      console.log(`   - 缓存命中率: ${(stats.cacheStats.total.hitRate * 100).toFixed(1)}%`);
      console.log(`   - 缓存项目: ${stats.cacheStats.total.totalItems} 个`);
    } catch (error) {
      console.error('超级优化V2版本测试失败:', error);
    }

    console.log('\n🏆 性能基准测试V2完成');

    // 清理资源
    await superOptimizedArbitrageFinder.cleanup();
  });

async function main() {
  try {
    await program.parseAsync(process.argv);
  } catch (error) {
    console.error('命令执行出错:', error);
    process.exit(1);
  }
}

main();
