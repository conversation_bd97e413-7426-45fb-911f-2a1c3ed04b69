import { type Address, formatEther, parseEther } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { getTokenAddress } from '../constants/tokens';
import type { ArbitrageConfig, ArbitrageOpportunity, ArbitrageStats, PriceQueryResult } from '../types';
import { ARBITRAGE_PATHS, GAS_PRICE_GWEI, MAX_SLIPPAGE_PERCENT, MIN_PROFIT_THRESHOLD_USD } from '../utils/config';
import { type BatchPoolStateResult, BatchPoolStateService } from './BatchPoolStateService';

import { DirectV3PoolSwapService } from './DirectV3PoolSwapService';
import { type MulticallPoolData, MulticallService, type MulticallTokenData } from './MulticallService';
import { DexVersion, UnifiedDexService } from './UnifiedDexService';
import { WebSocketMulticallService } from './WebSocketMulticallService';

/**
 * 套利机会发现器
 * 负责监控价格、计算套利机会并执行交易
 */
export class ArbitrageFinder {
  private config: ArbitrageConfig;
  private stats: ArbitrageStats;
  private isMonitoring = false;
  private monitoringInterval?: NodeJS.Timeout;
  private dexService: UnifiedDexService;
  private directV3Service: DirectV3PoolSwapService;
  private batchPoolService: BatchPoolStateService;
  private multicallService: MulticallService;
  private wsMulticallService: WebSocketMulticallService;

  constructor() {
    this.dexService = new UnifiedDexService();
    this.directV3Service = new DirectV3PoolSwapService();
    this.batchPoolService = new BatchPoolStateService();
    this.multicallService = new MulticallService();
    this.wsMulticallService = new WebSocketMulticallService();
    this.config = {
      paths: ARBITRAGE_PATHS,
      minProfitThresholdUSD: MIN_PROFIT_THRESHOLD_USD,
      maxSlippagePercent: MAX_SLIPPAGE_PERCENT,
      gasPriceGwei: GAS_PRICE_GWEI,
      checkIntervalMs: 10000, // 10 seconds
    };

    this.stats = {
      totalChecks: 0,
      opportunitiesFound: 0,
      successfulTrades: 0,
      failedTrades: 0,
      totalProfitUSD: 0,
      averageCheckDuration: 0,
      lastCheckTime: new Date(),
    };

    console.log('🔍 ArbitrageFinder 初始化完成');
    console.log(`   - 监控路径数量: ${this.config.paths.length}`);
    console.log(`   - 最小利润阈值: $${this.config.minProfitThresholdUSD}`);
    console.log(`   - 最大滑点: ${this.config.maxSlippagePercent}%`);
  }

  /**
   * 执行单次套利检查
   */
  async checkOnce(): Promise<ArbitrageOpportunity[]> {
    const startTime = Date.now();
    console.log('\n🔍 开始套利机会检查...');

    const opportunities: ArbitrageOpportunity[] = [];

    try {
      for (let i = 0; i < this.config.paths.length; i++) {
        const path = this.config.paths[i];
        console.log(`\n📊 检查路径 ${i + 1}/${this.config.paths.length}: ${path.join(' -> ')}`);

        try {
          const opportunity = await this.checkTriangularArbitrage(path);
          if (opportunity?.profitable) {
            opportunities.push(opportunity);
            console.log(`✅ 发现套利机会! 预期利润: $${opportunity.estimatedProfitUSD.toFixed(4)}`);
          } else {
            console.log('❌ 无套利机会');
          }
        } catch (error) {
          console.log(`❌ 路径检查失败: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      // 更新统计信息
      this.stats.totalChecks++;
      this.stats.opportunitiesFound += opportunities.length;
      this.stats.lastCheckTime = new Date();

      const duration = Date.now() - startTime;
      this.stats.averageCheckDuration =
        (this.stats.averageCheckDuration * (this.stats.totalChecks - 1) + duration) / this.stats.totalChecks;

      console.log(`\n📈 检查完成 (耗时: ${duration}ms)`);
      console.log(`   - 发现机会: ${opportunities.length}`);
      console.log(`   - 总检查次数: ${this.stats.totalChecks}`);

      return opportunities;
    } catch (error) {
      console.error('❌ 套利检查失败:', error);
      return [];
    }
  }

  /**
   * 生成反向套利路径
   * 例如: [A, B, C, A] -> [A, C, B, A]
   */
  private generateReversePath(path: Address[]): Address[] {
    if (path.length !== 4) {
      throw new Error('路径必须包含4个代币');
    }

    const [tokenA, tokenB, tokenC] = path;
    return [tokenA, tokenC, tokenB, tokenA];
  }

  /**
   * 检查三角套利机会（包含正向和反向路径）
   */
  private async checkTriangularArbitrage(path: Address[]): Promise<ArbitrageOpportunity | null> {
    if (path.length !== 4) {
      throw new Error('三角套利路径必须包含4个代币 (A->B->C->A)');
    }

    // 验证首尾代币相同
    if (path[0] !== path[3]) {
      throw new Error('三角套利路径的首尾代币必须相同');
    }

    const inputAmount = parseEther('100'); // 使用 100 个代币作为测试金额

    console.log(`   🔄 检查路径: ${path.map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`).join(' -> ')}`);

    try {
      // 生成反向路径
      const reversePath = this.generateReversePath(path);

      console.log(
        `   📈 正向路径: ${path
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')}`,
      );
      console.log(
        `   📉 反向路径: ${reversePath
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')}`,
      );

      // 检查正向路径
      const forwardOpportunity = await this.checkSingleDirection(path, inputAmount, '正向');

      // 检查反向路径
      const reverseOpportunity = await this.checkSingleDirection(reversePath, inputAmount, '反向');

      // 比较两个方向，选择更优的
      let bestOpportunity: ArbitrageOpportunity | null = null;

      if (forwardOpportunity && reverseOpportunity) {
        if (forwardOpportunity.estimatedProfitUSD > reverseOpportunity.estimatedProfitUSD) {
          bestOpportunity = forwardOpportunity;
          console.log(
            `   🏆 选择正向路径 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)} vs $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        } else {
          bestOpportunity = reverseOpportunity;
          console.log(
            `   🏆 选择反向路径 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)} vs $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        }
      } else if (forwardOpportunity) {
        bestOpportunity = forwardOpportunity;
        console.log(`   ✅ 仅正向路径可行 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else if (reverseOpportunity) {
        bestOpportunity = reverseOpportunity;
        console.log(`   ✅ 仅反向路径可行 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else {
        console.log('   ❌ 正向和反向路径都不可行');
      }

      return bestOpportunity;
    } catch (error) {
      console.log(`   ❌ 路径检查失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 检查单个方向的套利机会
   */
  private async checkSingleDirection(
    path: Address[],
    inputAmount: bigint,
    direction: string,
  ): Promise<ArbitrageOpportunity | null> {
    const [tokenA, tokenB, tokenC] = path; // 只取前3个代币

    try {
      // 使用统一 DexService 获取最佳路径组合
      const result = await this.dexService.getTriangularArbitragePaths(tokenA, tokenB, tokenC, inputAmount);

      if (!result.path1 || !result.path2 || !result.path3) {
        console.log(`   ❌ ${direction}路径: 无法完成完整的三角套利路径`);
        return null;
      }

      // 显示每步的详细信息
      console.log(
        `   📊 ${direction}路径步骤1 (${result.path1.version}): ${formatEther(inputAmount)} -> ${formatEther(result.path1.outputAmount)}`,
      );
      console.log(
        `   📊 ${direction}路径步骤2 (${result.path2.version}): ${formatEther(result.path1.outputAmount)} -> ${formatEther(result.path2.outputAmount)}`,
      );
      console.log(
        `   📊 ${direction}路径步骤3 (${result.path3.version}): ${formatEther(result.path2.outputAmount)} -> ${formatEther(result.path3.outputAmount)}`,
      );

      // 计算利润
      const profit = result.totalOutput > inputAmount ? result.totalOutput - inputAmount : BigInt(0);
      const profitPercentage = Number((profit * BigInt(10000)) / inputAmount) / 100; // 转换为百分比

      // 估算 Gas 费用 (简化计算)
      const estimatedGasCost = (BigInt(300000) * parseEther(this.config.gasPriceGwei.toString())) / BigInt(1e9);
      const estimatedGasCostUSD = Number(formatEther(estimatedGasCost)) * 300; // 假设 BNB = $300

      // 估算利润 USD (简化计算，假设代币价值)
      const estimatedProfitUSD = Number(formatEther(profit)) * 1; // 假设代币 = $1

      const netProfitUSD = estimatedProfitUSD - estimatedGasCostUSD;
      const profitable = netProfitUSD > this.config.minProfitThresholdUSD;

      console.log(`   💰 ${direction}路径利润: ${formatEther(profit)} (${profitPercentage.toFixed(4)}%)`);
      console.log(`   💵 ${direction}路径净利润: $${netProfitUSD.toFixed(4)}`);

      if (!profitable) {
        return null;
      }

      return {
        path,
        inputAmount,
        outputAmount: result.totalOutput,
        profit,
        profitPercentage,
        estimatedGasCostUSD,
        estimatedProfitUSD: netProfitUSD,
        profitable,
        timestamp: new Date(),
        priceQueries: [
          {
            path: [tokenA, tokenB],
            inputAmount,
            outputAmount: result.path1.outputAmount,
            version: result.path1.version,
            fee: result.path1.fee,
          },
          {
            path: [tokenB, tokenC],
            inputAmount: result.path1.outputAmount,
            outputAmount: result.path2.outputAmount,
            version: result.path2.version,
            fee: result.path2.fee,
          },
          {
            path: [tokenC, tokenA],
            inputAmount: result.path2.outputAmount,
            outputAmount: result.path3.outputAmount,
            version: result.path3.version,
            fee: result.path3.fee,
          },
        ] as PriceQueryResult[],
      };
    } catch (error) {
      console.log(`   ❌ ${direction}路径价格查询失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 执行单次套利检查（增强版，使用 DirectV3PoolSwapService）
   */
  async checkOnceEnhanced(): Promise<ArbitrageOpportunity[]> {
    const startTime = Date.now();
    console.log('\n🔍 开始增强版套利机会检查 (使用 DirectV3PoolSwapService)...');

    const opportunities: ArbitrageOpportunity[] = [];

    try {
      for (let i = 0; i < this.config.paths.length; i++) {
        const path = this.config.paths[i];
        console.log(`\n📊 检查路径 ${i + 1}/${this.config.paths.length}: ${path.join(' -> ')}`);

        try {
          const opportunity = await this.checkTriangularArbitrageEnhanced(path);
          if (opportunity?.profitable) {
            opportunities.push(opportunity);
            console.log(`✅ 发现套利机会! 预期利润: $${opportunity.estimatedProfitUSD.toFixed(4)}`);
          } else {
            console.log('❌ 无套利机会');
          }
        } catch (error) {
          console.log(`❌ 路径检查失败: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      // 更新统计信息
      this.stats.totalChecks++;
      this.stats.opportunitiesFound += opportunities.length;
      this.stats.lastCheckTime = new Date();

      const duration = Date.now() - startTime;
      this.stats.averageCheckDuration =
        (this.stats.averageCheckDuration * (this.stats.totalChecks - 1) + duration) / this.stats.totalChecks;

      console.log(`\n📈 增强版检查完成 (耗时: ${duration}ms)`);
      console.log(`   - 发现机会: ${opportunities.length}`);
      console.log(`   - 总检查次数: ${this.stats.totalChecks}`);

      return opportunities;
    } catch (error) {
      console.error('❌ 增强版套利检查失败:', error);
      return [];
    }
  }

  /**
   * 检查三角套利机会（增强版，专门针对 BNB-BANK-USD1 路径）
   */
  private async checkTriangularArbitrageEnhanced(path: Address[]): Promise<ArbitrageOpportunity | null> {
    if (path.length !== 4) {
      throw new Error('三角套利路径必须包含4个代币 (A->B->C->A)');
    }

    // 验证首尾代币相同
    if (path[0] !== path[3]) {
      throw new Error('三角套利路径的首尾代币必须相同');
    }

    const inputAmount = parseEther('1'); // 使用 1 个代币作为测试金额

    console.log(`   🔄 检查路径: ${path.map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`).join(' -> ')}`);

    try {
      // 生成反向路径
      const reversePath = this.generateReversePath(path);

      console.log(
        `   📈 正向路径: ${path
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')}`,
      );
      console.log(
        `   📉 反向路径: ${reversePath
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')}`,
      );

      // 检查正向路径
      const forwardOpportunity = await this.checkSingleDirectionEnhanced(path, inputAmount, '正向');

      // 检查反向路径
      const reverseOpportunity = await this.checkSingleDirectionEnhanced(reversePath, inputAmount, '反向');

      // 比较两个方向，选择更优的
      let bestOpportunity: ArbitrageOpportunity | null = null;

      if (forwardOpportunity && reverseOpportunity) {
        if (forwardOpportunity.estimatedProfitUSD > reverseOpportunity.estimatedProfitUSD) {
          bestOpportunity = forwardOpportunity;
          console.log(
            `   🏆 选择正向路径 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)} vs $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        } else {
          bestOpportunity = reverseOpportunity;
          console.log(
            `   🏆 选择反向路径 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)} vs $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        }
      } else if (forwardOpportunity) {
        bestOpportunity = forwardOpportunity;
        console.log(`   ✅ 仅正向路径可行 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else if (reverseOpportunity) {
        bestOpportunity = reverseOpportunity;
        console.log(`   ✅ 仅反向路径可行 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else {
        console.log('   ❌ 正向和反向路径都不可行');
      }

      return bestOpportunity;
    } catch (error) {
      console.log(`   ❌ 路径检查失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 检查单个方向的套利机会（增强版，使用 DirectV3PoolSwapService）
   */
  private async checkSingleDirectionEnhanced(
    path: Address[],
    inputAmount: bigint,
    direction: string,
  ): Promise<ArbitrageOpportunity | null> {
    const [tokenA, tokenB, tokenC] = path; // 只取前3个代币

    try {
      // 步骤 1: A -> B
      let step1Result: { estimatedAmount0: bigint; estimatedAmount1: bigint; priceImpact: number } | null = null;
      let step1Pool: Address | null = null;
      let step1ZeroForOne = false;

      // 确定第一步的池子和方向
      if (tokenA === getTokenAddress('WBNB') && tokenB === getTokenAddress('BANK')) {
        step1Pool = BSC_CONTRACTS.BNB_BANK_POOL as Address;
        step1ZeroForOne = false; // WBNB -> BANK
      } else if (tokenA === getTokenAddress('BANK') && tokenB === getTokenAddress('WBNB')) {
        step1Pool = BSC_CONTRACTS.BNB_BANK_POOL as Address;
        step1ZeroForOne = true; // BANK -> WBNB
      } else if (tokenA === getTokenAddress('WBNB') && tokenB === getTokenAddress('USD1')) {
        step1Pool = BSC_CONTRACTS.BNB_USD1_POOL as Address;
        step1ZeroForOne = false; // WBNB -> USD1
      } else if (tokenA === getTokenAddress('USD1') && tokenB === getTokenAddress('WBNB')) {
        step1Pool = BSC_CONTRACTS.BNB_USD1_POOL as Address;
        step1ZeroForOne = true; // USD1 -> WBNB
      } else if (tokenA === getTokenAddress('BANK') && tokenB === getTokenAddress('USD1')) {
        step1Pool = BSC_CONTRACTS.USD1_BANK_POOL as Address;
        step1ZeroForOne = true; // BANK -> USD1
      } else if (tokenA === getTokenAddress('USD1') && tokenB === getTokenAddress('BANK')) {
        step1Pool = BSC_CONTRACTS.USD1_BANK_POOL as Address;
        step1ZeroForOne = false; // USD1 -> BANK
      }

      if (!step1Pool) {
        console.log(`   ❌ ${direction}路径: 不支持的第一步交易对 ${tokenA} -> ${tokenB}`);
        return null;
      }

      step1Result = await this.directV3Service.simulateSwap(step1Pool, step1ZeroForOne, inputAmount);
      if (!step1Result) {
        console.log(`   ❌ ${direction}路径: 第一步模拟失败`);
        return null;
      }

      const step1Output = step1ZeroForOne
        ? step1Result.estimatedAmount1 < 0
          ? -step1Result.estimatedAmount1
          : step1Result.estimatedAmount1
        : step1Result.estimatedAmount0 < 0
          ? -step1Result.estimatedAmount0
          : step1Result.estimatedAmount0;

      console.log(`   📊 ${direction}路径步骤1: ${formatEther(inputAmount)} -> ${formatEther(step1Output)}`);

      // 步骤 2: B -> C
      let step2Result: { estimatedAmount0: bigint; estimatedAmount1: bigint; priceImpact: number } | null = null;
      let step2Pool: Address | null = null;
      let step2ZeroForOne = false;

      // 确定第二步的池子和方向
      if (tokenB === getTokenAddress('WBNB') && tokenC === getTokenAddress('BANK')) {
        step2Pool = BSC_CONTRACTS.BNB_BANK_POOL as Address;
        step2ZeroForOne = false; // WBNB -> BANK
      } else if (tokenB === getTokenAddress('BANK') && tokenC === getTokenAddress('WBNB')) {
        step2Pool = BSC_CONTRACTS.BNB_BANK_POOL as Address;
        step2ZeroForOne = true; // BANK -> WBNB
      } else if (tokenB === getTokenAddress('WBNB') && tokenC === getTokenAddress('USD1')) {
        step2Pool = BSC_CONTRACTS.BNB_USD1_POOL as Address;
        step2ZeroForOne = false; // WBNB -> USD1
      } else if (tokenB === getTokenAddress('USD1') && tokenC === getTokenAddress('WBNB')) {
        step2Pool = BSC_CONTRACTS.BNB_USD1_POOL as Address;
        step2ZeroForOne = true; // USD1 -> WBNB
      } else if (tokenB === getTokenAddress('BANK') && tokenC === getTokenAddress('USD1')) {
        step2Pool = BSC_CONTRACTS.USD1_BANK_POOL as Address;
        step2ZeroForOne = true; // BANK -> USD1
      } else if (tokenB === getTokenAddress('USD1') && tokenC === getTokenAddress('BANK')) {
        step2Pool = BSC_CONTRACTS.USD1_BANK_POOL as Address;
        step2ZeroForOne = false; // USD1 -> BANK
      }

      if (!step2Pool) {
        console.log(`   ❌ ${direction}路径: 不支持的第二步交易对 ${tokenB} -> ${tokenC}`);
        return null;
      }

      step2Result = await this.directV3Service.simulateSwap(step2Pool, step2ZeroForOne, step1Output);
      if (!step2Result) {
        console.log(`   ❌ ${direction}路径: 第二步模拟失败`);
        return null;
      }

      const step2Output = step2ZeroForOne
        ? step2Result.estimatedAmount1 < 0
          ? -step2Result.estimatedAmount1
          : step2Result.estimatedAmount1
        : step2Result.estimatedAmount0 < 0
          ? -step2Result.estimatedAmount0
          : step2Result.estimatedAmount0;

      console.log(`   📊 ${direction}路径步骤2: ${formatEther(step1Output)} -> ${formatEther(step2Output)}`);

      // 步骤 3: C -> A
      let step3Result: { estimatedAmount0: bigint; estimatedAmount1: bigint; priceImpact: number } | null = null;
      let step3Pool: Address | null = null;
      let step3ZeroForOne = false;

      // 确定第三步的池子和方向
      if (tokenC === getTokenAddress('WBNB') && tokenA === getTokenAddress('BANK')) {
        step3Pool = BSC_CONTRACTS.BNB_BANK_POOL as Address;
        step3ZeroForOne = false; // WBNB -> BANK
      } else if (tokenC === getTokenAddress('BANK') && tokenA === getTokenAddress('WBNB')) {
        step3Pool = BSC_CONTRACTS.BNB_BANK_POOL as Address;
        step3ZeroForOne = true; // BANK -> WBNB
      } else if (tokenC === getTokenAddress('WBNB') && tokenA === getTokenAddress('USD1')) {
        step3Pool = BSC_CONTRACTS.BNB_USD1_POOL as Address;
        step3ZeroForOne = false; // WBNB -> USD1
      } else if (tokenC === getTokenAddress('USD1') && tokenA === getTokenAddress('WBNB')) {
        step3Pool = BSC_CONTRACTS.BNB_USD1_POOL as Address;
        step3ZeroForOne = true; // USD1 -> WBNB
      } else if (tokenC === getTokenAddress('BANK') && tokenA === getTokenAddress('USD1')) {
        step3Pool = BSC_CONTRACTS.USD1_BANK_POOL as Address;
        step3ZeroForOne = true; // BANK -> USD1
      } else if (tokenC === getTokenAddress('USD1') && tokenA === getTokenAddress('BANK')) {
        step3Pool = BSC_CONTRACTS.USD1_BANK_POOL as Address;
        step3ZeroForOne = false; // USD1 -> BANK
      }

      if (!step3Pool) {
        console.log(`   ❌ ${direction}路径: 不支持的第三步交易对 ${tokenC} -> ${tokenA}`);
        return null;
      }

      step3Result = await this.directV3Service.simulateSwap(step3Pool, step3ZeroForOne, step2Output);
      if (!step3Result) {
        console.log(`   ❌ ${direction}路径: 第三步模拟失败`);
        return null;
      }

      const step3Output = step3ZeroForOne
        ? step3Result.estimatedAmount1 < 0
          ? -step3Result.estimatedAmount1
          : step3Result.estimatedAmount1
        : step3Result.estimatedAmount0 < 0
          ? -step3Result.estimatedAmount0
          : step3Result.estimatedAmount0;

      console.log(`   📊 ${direction}路径步骤3: ${formatEther(step2Output)} -> ${formatEther(step3Output)}`);

      // 计算利润
      const profit = step3Output > inputAmount ? step3Output - inputAmount : BigInt(0);
      const profitPercentage = Number((profit * BigInt(10000)) / inputAmount) / 100; // 转换为百分比

      // 估算 Gas 费用 (简化计算)
      const estimatedGasCost = (BigInt(500000) * parseEther(this.config.gasPriceGwei.toString())) / BigInt(1e9);
      const estimatedGasCostUSD = Number(formatEther(estimatedGasCost)) * 600; // 假设 BNB = $600

      // 估算利润 USD (假设 BNB = $600)
      const estimatedProfitUSD = Number(formatEther(profit)) * 600;

      const netProfitUSD = estimatedProfitUSD - estimatedGasCostUSD;
      const profitable = netProfitUSD > this.config.minProfitThresholdUSD;

      console.log(`   💰 ${direction}路径利润: ${formatEther(profit)} (${profitPercentage.toFixed(4)}%)`);
      console.log(`   💵 ${direction}路径净利润: $${netProfitUSD.toFixed(4)}`);

      if (!profitable) {
        return null;
      }

      return {
        path,
        inputAmount,
        outputAmount: step3Output,
        profit,
        profitPercentage,
        estimatedGasCostUSD,
        estimatedProfitUSD: netProfitUSD,
        profitable,
        timestamp: new Date(),
        priceQueries: [
          {
            path: [tokenA, tokenB],
            inputAmount,
            outputAmount: step1Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenB, tokenC],
            inputAmount: step1Output,
            outputAmount: step2Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenC, tokenA],
            inputAmount: step2Output,
            outputAmount: step3Output,
            version: DexVersion.V3,
            fee: 0,
          },
        ] as PriceQueryResult[],
      };
    } catch (error) {
      console.log(`   ❌ ${direction}路径价格查询失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 执行并行化的增强版套利检查
   */
  async checkOnceParallel(): Promise<ArbitrageOpportunity[]> {
    const startTime = Date.now();
    console.log('\n🚀 开始并行化增强版套利机会检查...');

    const opportunities: ArbitrageOpportunity[] = [];

    try {
      // 并行检查所有路径
      const pathPromises = this.config.paths.map(async (path, index) => {
        const pathStartTime = Date.now();
        console.log(`📊 开始检查路径 ${index + 1}/${this.config.paths.length}: ${path.join(' -> ')}`);

        try {
          const opportunity = await this.checkTriangularArbitrageParallel(path);
          const pathDuration = Date.now() - pathStartTime;

          if (opportunity?.profitable) {
            console.log(
              `✅ 路径 ${index + 1} 发现套利机会! 预期利润: $${opportunity.estimatedProfitUSD.toFixed(4)} (耗时: ${pathDuration}ms)`,
            );
            return opportunity;
          }
          console.log(`❌ 路径 ${index + 1} 无套利机会 (耗时: ${pathDuration}ms)`);
          return null;
        } catch (error) {
          const pathDuration = Date.now() - pathStartTime;
          console.log(
            `❌ 路径 ${index + 1} 检查失败: ${error instanceof Error ? error.message : String(error)} (耗时: ${pathDuration}ms)`,
          );
          return null;
        }
      });

      // 等待所有路径检查完成
      const results = await Promise.allSettled(pathPromises);

      // 收集成功的结果
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value) {
          opportunities.push(result.value);
        }
      }

      // 更新统计信息
      this.stats.totalChecks++;
      this.stats.opportunitiesFound += opportunities.length;
      this.stats.lastCheckTime = new Date();

      const duration = Date.now() - startTime;
      this.stats.averageCheckDuration =
        (this.stats.averageCheckDuration * (this.stats.totalChecks - 1) + duration) / this.stats.totalChecks;

      console.log(`\n🎯 并行化检查完成 (总耗时: ${duration}ms)`);
      console.log(`   - 发现机会: ${opportunities.length}`);
      console.log(`   - 总检查次数: ${this.stats.totalChecks}`);
      console.log(`   - 平均检查耗时: ${this.stats.averageCheckDuration.toFixed(0)}ms`);

      return opportunities;
    } catch (error) {
      console.error('❌ 并行化套利检查失败:', error);
      return [];
    }
  }

  /**
   * 并行化检查三角套利机会（正向和反向路径同时检查）
   */
  private async checkTriangularArbitrageParallel(path: Address[]): Promise<ArbitrageOpportunity | null> {
    if (path.length !== 4) {
      throw new Error('三角套利路径必须包含4个代币 (A->B->C->A)');
    }

    // 验证首尾代币相同
    if (path[0] !== path[3]) {
      throw new Error('三角套利路径的首尾代币必须相同');
    }

    const inputAmount = parseEther('1'); // 使用 1 个代币作为测试金额

    try {
      // 生成反向路径
      const reversePath = this.generateReversePath(path);

      console.log(
        `   🔄 并行检查: 正向 ${path
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')} | 反向 ${reversePath
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')}`,
      );

      // 并行检查正向和反向路径
      const [forwardResult, reverseResult] = await Promise.allSettled([
        this.checkSingleDirectionParallel(path, inputAmount, '正向'),
        this.checkSingleDirectionParallel(reversePath, inputAmount, '反向'),
      ]);

      // 提取结果
      const forwardOpportunity = forwardResult.status === 'fulfilled' ? forwardResult.value : null;
      const reverseOpportunity = reverseResult.status === 'fulfilled' ? reverseResult.value : null;

      // 记录错误（如果有）
      if (forwardResult.status === 'rejected') {
        console.log(`   ⚠️ 正向路径检查失败: ${forwardResult.reason}`);
      }
      if (reverseResult.status === 'rejected') {
        console.log(`   ⚠️ 反向路径检查失败: ${reverseResult.reason}`);
      }

      // 比较两个方向，选择更优的
      let bestOpportunity: ArbitrageOpportunity | null = null;

      if (forwardOpportunity && reverseOpportunity) {
        if (forwardOpportunity.estimatedProfitUSD > reverseOpportunity.estimatedProfitUSD) {
          bestOpportunity = forwardOpportunity;
          console.log(
            `   🏆 选择正向路径 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)} vs $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        } else {
          bestOpportunity = reverseOpportunity;
          console.log(
            `   🏆 选择反向路径 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)} vs $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        }
      } else if (forwardOpportunity) {
        bestOpportunity = forwardOpportunity;
        console.log(`   ✅ 仅正向路径可行 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else if (reverseOpportunity) {
        bestOpportunity = reverseOpportunity;
        console.log(`   ✅ 仅反向路径可行 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else {
        console.log('   ❌ 正向和反向路径都不可行');
      }

      return bestOpportunity;
    } catch (error) {
      console.log(`   ❌ 路径检查失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 并行化检查单个方向的套利机会（三步swap并行查询）
   */
  private async checkSingleDirectionParallel(
    path: Address[],
    inputAmount: bigint,
    direction: string,
  ): Promise<ArbitrageOpportunity | null> {
    const [tokenA, tokenB, tokenC] = path; // 只取前3个代币

    try {
      // 预先确定所有三步的池子和方向
      const swapConfigs = this.getSwapConfigurations(tokenA, tokenB, tokenC);

      if (!swapConfigs) {
        console.log(`   ❌ ${direction}路径: 不支持的代币组合`);
        return null;
      }

      // 第一步必须先执行，因为后续步骤依赖其结果
      const step1Result = await this.directV3Service.simulateSwap(
        swapConfigs.step1.pool,
        swapConfigs.step1.zeroForOne,
        inputAmount,
      );

      if (!step1Result) {
        console.log(`   ❌ ${direction}路径: 第一步模拟失败`);
        return null;
      }

      const step1Output = swapConfigs.step1.zeroForOne
        ? step1Result.estimatedAmount1 < 0
          ? -step1Result.estimatedAmount1
          : step1Result.estimatedAmount1
        : step1Result.estimatedAmount0 < 0
          ? -step1Result.estimatedAmount0
          : step1Result.estimatedAmount0;

      // 并行执行第二步和第三步的准备工作（虽然第三步还需要第二步的结果）
      const step2Result = await this.directV3Service.simulateSwap(
        swapConfigs.step2.pool,
        swapConfigs.step2.zeroForOne,
        step1Output,
      );

      if (!step2Result) {
        console.log(`   ❌ ${direction}路径: 第二步模拟失败`);
        return null;
      }

      const step2Output = swapConfigs.step2.zeroForOne
        ? step2Result.estimatedAmount1 < 0
          ? -step2Result.estimatedAmount1
          : step2Result.estimatedAmount1
        : step2Result.estimatedAmount0 < 0
          ? -step2Result.estimatedAmount0
          : step2Result.estimatedAmount0;

      // 第三步
      const step3Result = await this.directV3Service.simulateSwap(
        swapConfigs.step3.pool,
        swapConfigs.step3.zeroForOne,
        step2Output,
      );

      if (!step3Result) {
        console.log(`   ❌ ${direction}路径: 第三步模拟失败`);
        return null;
      }

      const step3Output = swapConfigs.step3.zeroForOne
        ? step3Result.estimatedAmount1 < 0
          ? -step3Result.estimatedAmount1
          : step3Result.estimatedAmount1
        : step3Result.estimatedAmount0 < 0
          ? -step3Result.estimatedAmount0
          : step3Result.estimatedAmount0;

      console.log(
        `   📊 ${direction}: ${formatEther(inputAmount)} → ${formatEther(step1Output)} → ${formatEther(step2Output)} → ${formatEther(step3Output)}`,
      );

      // 计算利润
      const profit = step3Output > inputAmount ? step3Output - inputAmount : BigInt(0);
      const profitPercentage = Number((profit * BigInt(10000)) / inputAmount) / 100;

      // 估算 Gas 费用和利润
      const estimatedGasCost = (BigInt(500000) * parseEther(this.config.gasPriceGwei.toString())) / BigInt(1e9);
      const estimatedGasCostUSD = Number(formatEther(estimatedGasCost)) * 600; // 假设 BNB = $600
      const estimatedProfitUSD = Number(formatEther(profit)) * 600;
      const netProfitUSD = estimatedProfitUSD - estimatedGasCostUSD;
      const profitable = netProfitUSD > this.config.minProfitThresholdUSD;

      console.log(
        `   💰 ${direction}利润: ${formatEther(profit)} (${profitPercentage.toFixed(4)}%) → $${netProfitUSD.toFixed(4)}`,
      );

      if (!profitable) {
        return null;
      }

      return {
        path,
        inputAmount,
        outputAmount: step3Output,
        profit,
        profitPercentage,
        estimatedGasCostUSD,
        estimatedProfitUSD: netProfitUSD,
        profitable,
        timestamp: new Date(),
        priceQueries: [
          {
            path: [tokenA, tokenB],
            inputAmount,
            outputAmount: step1Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenB, tokenC],
            inputAmount: step1Output,
            outputAmount: step2Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenC, tokenA],
            inputAmount: step2Output,
            outputAmount: step3Output,
            version: DexVersion.V3,
            fee: 0,
          },
        ] as PriceQueryResult[],
      };
    } catch (error) {
      console.log(`   ❌ ${direction}路径价格查询失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 获取三步swap的配置信息
   */
  private getSwapConfigurations(
    tokenA: Address,
    tokenB: Address,
    tokenC: Address,
  ): {
    step1: { pool: Address; zeroForOne: boolean };
    step2: { pool: Address; zeroForOne: boolean };
    step3: { pool: Address; zeroForOne: boolean };
  } | null {
    const configs = {
      step1: { pool: null as Address | null, zeroForOne: false },
      step2: { pool: null as Address | null, zeroForOne: false },
      step3: { pool: null as Address | null, zeroForOne: false },
    };

    // 第一步: A -> B
    if (tokenA === getTokenAddress('WBNB') && tokenB === getTokenAddress('BANK')) {
      configs.step1 = { pool: BSC_CONTRACTS.BNB_BANK_POOL as Address, zeroForOne: false };
    } else if (tokenA === getTokenAddress('BANK') && tokenB === getTokenAddress('WBNB')) {
      configs.step1 = { pool: BSC_CONTRACTS.BNB_BANK_POOL as Address, zeroForOne: true };
    } else if (tokenA === getTokenAddress('WBNB') && tokenB === getTokenAddress('USD1')) {
      configs.step1 = { pool: BSC_CONTRACTS.BNB_USD1_POOL as Address, zeroForOne: false };
    } else if (tokenA === getTokenAddress('USD1') && tokenB === getTokenAddress('WBNB')) {
      configs.step1 = { pool: BSC_CONTRACTS.BNB_USD1_POOL as Address, zeroForOne: true };
    } else if (tokenA === getTokenAddress('BANK') && tokenB === getTokenAddress('USD1')) {
      configs.step1 = { pool: BSC_CONTRACTS.USD1_BANK_POOL as Address, zeroForOne: true };
    } else if (tokenA === getTokenAddress('USD1') && tokenB === getTokenAddress('BANK')) {
      configs.step1 = { pool: BSC_CONTRACTS.USD1_BANK_POOL as Address, zeroForOne: false };
    }

    // 第二步: B -> C
    if (tokenB === getTokenAddress('WBNB') && tokenC === getTokenAddress('BANK')) {
      configs.step2 = { pool: BSC_CONTRACTS.BNB_BANK_POOL as Address, zeroForOne: false };
    } else if (tokenB === getTokenAddress('BANK') && tokenC === getTokenAddress('WBNB')) {
      configs.step2 = { pool: BSC_CONTRACTS.BNB_BANK_POOL as Address, zeroForOne: true };
    } else if (tokenB === getTokenAddress('WBNB') && tokenC === getTokenAddress('USD1')) {
      configs.step2 = { pool: BSC_CONTRACTS.BNB_USD1_POOL as Address, zeroForOne: false };
    } else if (tokenB === getTokenAddress('USD1') && tokenC === getTokenAddress('WBNB')) {
      configs.step2 = { pool: BSC_CONTRACTS.BNB_USD1_POOL as Address, zeroForOne: true };
    } else if (tokenB === getTokenAddress('BANK') && tokenC === getTokenAddress('USD1')) {
      configs.step2 = { pool: BSC_CONTRACTS.USD1_BANK_POOL as Address, zeroForOne: true };
    } else if (tokenB === getTokenAddress('USD1') && tokenC === getTokenAddress('BANK')) {
      configs.step2 = { pool: BSC_CONTRACTS.USD1_BANK_POOL as Address, zeroForOne: false };
    }

    // 第三步: C -> A
    if (tokenC === getTokenAddress('WBNB') && tokenA === getTokenAddress('BANK')) {
      configs.step3 = { pool: BSC_CONTRACTS.BNB_BANK_POOL as Address, zeroForOne: false };
    } else if (tokenC === getTokenAddress('BANK') && tokenA === getTokenAddress('WBNB')) {
      configs.step3 = { pool: BSC_CONTRACTS.BNB_BANK_POOL as Address, zeroForOne: true };
    } else if (tokenC === getTokenAddress('WBNB') && tokenA === getTokenAddress('USD1')) {
      configs.step3 = { pool: BSC_CONTRACTS.BNB_USD1_POOL as Address, zeroForOne: false };
    } else if (tokenC === getTokenAddress('USD1') && tokenA === getTokenAddress('WBNB')) {
      configs.step3 = { pool: BSC_CONTRACTS.BNB_USD1_POOL as Address, zeroForOne: true };
    } else if (tokenC === getTokenAddress('BANK') && tokenA === getTokenAddress('USD1')) {
      configs.step3 = { pool: BSC_CONTRACTS.USD1_BANK_POOL as Address, zeroForOne: true };
    } else if (tokenC === getTokenAddress('USD1') && tokenA === getTokenAddress('BANK')) {
      configs.step3 = { pool: BSC_CONTRACTS.USD1_BANK_POOL as Address, zeroForOne: false };
    }

    // 检查所有配置是否都已设置
    if (!configs.step1.pool || !configs.step2.pool || !configs.step3.pool) {
      return null;
    }

    return configs as {
      step1: { pool: Address; zeroForOne: boolean };
      step2: { pool: Address; zeroForOne: boolean };
      step3: { pool: Address; zeroForOne: boolean };
    };
  }

  /**
   * 开始持续监控
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      console.log('⚠️ 监控已在运行中');
      return;
    }

    this.isMonitoring = true;
    console.log(`🚀 开始持续监控 (间隔: ${this.config.checkIntervalMs}ms)`);

    this.monitoringInterval = setInterval(async () => {
      try {
        const opportunities = await this.checkOnce();

        // 如果发现盈利机会，这里可以添加执行逻辑
        for (const opportunity of opportunities) {
          if (opportunity.profitable) {
            console.log('\n🎯 发现盈利机会!');
            console.log(`   路径: ${opportunity.path.join(' -> ')}`);
            console.log(`   预期利润: $${opportunity.estimatedProfitUSD.toFixed(4)}`);
            console.log(`   利润率: ${opportunity.profitPercentage.toFixed(4)}%`);

            // TODO: 在这里添加实际的交易执行逻辑
            console.log('   📝 注意: 实际交易执行功能尚未实现');
          }
        }
      } catch (error) {
        console.error('❌ 监控循环出错:', error);
      }
    }, this.config.checkIntervalMs);
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      console.log('⚠️ 监控未在运行');
      return;
    }

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    console.log('🛑 监控已停止');
  }

  /**
   * 获取统计信息
   */
  getStats(): ArbitrageStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalChecks: 0,
      opportunitiesFound: 0,
      successfulTrades: 0,
      failedTrades: 0,
      totalProfitUSD: 0,
      averageCheckDuration: 0,
      lastCheckTime: new Date(),
    };
    console.log('统计信息已重置');
  }

  /**
   * 执行超级并行化的套利检查（集成所有优化）
   */
  async checkOnceSuperParallel(): Promise<ArbitrageOpportunity[]> {
    const startTime = Date.now();
    console.log('\n🚀 开始超级并行化套利机会检查...');

    const opportunities: ArbitrageOpportunity[] = [];

    try {
      // 1. 预处理：收集所有需要的池子地址
      const allPoolAddresses = new Set<Address>();
      for (const path of this.config.paths) {
        const [tokenA, tokenB, tokenC] = path;

        // 为正向路径添加池子地址
        this.addPoolAddressesForPath(allPoolAddresses, tokenA, tokenB, tokenC);

        // 为反向路径添加池子地址
        const reversePath = this.generateReversePath(path);
        const [revTokenA, revTokenB, revTokenC] = reversePath;
        this.addPoolAddressesForPath(allPoolAddresses, revTokenA, revTokenB, revTokenC);
      }

      const poolAddressArray = Array.from(allPoolAddresses);
      console.log(`📊 预处理完成，需要查询 ${poolAddressArray.length} 个池子`);

      // 2. 批量预加载所有池子信息
      const preloadStartTime = Date.now();
      const poolInfos = await this.batchPoolService.batchGetCompletePoolInfo(poolAddressArray);
      const preloadDuration = Date.now() - preloadStartTime;

      console.log(`⚡ 数据预加载完成，耗时 ${preloadDuration}ms`);

      // 3. 创建池子信息映射表
      const poolInfoMap = new Map<Address, BatchPoolStateResult>();
      for (const poolInfo of poolInfos) {
        if (poolInfo.poolState) {
          poolInfoMap.set(poolInfo.poolAddress, poolInfo);
        }
      }

      // 4. 并行检查所有路径
      const pathPromises = this.config.paths.map(async (path, index) => {
        const pathStartTime = Date.now();
        console.log(`📊 开始检查路径 ${index + 1}/${this.config.paths.length}: ${path.join(' -> ')}`);

        try {
          const opportunity = await this.checkTriangularArbitrageSuperParallel(path, poolInfoMap);
          const pathDuration = Date.now() - pathStartTime;

          if (opportunity?.profitable) {
            console.log(
              `✅ 路径 ${index + 1} 发现套利机会! 预期利润: $${opportunity.estimatedProfitUSD.toFixed(4)} (耗时: ${pathDuration}ms)`,
            );
            return opportunity;
          }
          console.log(`❌ 路径 ${index + 1} 无套利机会 (耗时: ${pathDuration}ms)`);
          return null;
        } catch (error) {
          const pathDuration = Date.now() - pathStartTime;
          console.log(
            `❌ 路径 ${index + 1} 检查失败: ${error instanceof Error ? error.message : String(error)} (耗时: ${pathDuration}ms)`,
          );
          return null;
        }
      });

      // 5. 等待所有路径检查完成
      const results = await Promise.allSettled(pathPromises);

      // 6. 收集成功的结果
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value) {
          opportunities.push(result.value);
        }
      }

      // 7. 清理过期缓存
      this.batchPoolService.cleanupExpiredCache();

      // 8. 更新统计信息
      this.stats.totalChecks++;
      this.stats.opportunitiesFound += opportunities.length;
      this.stats.lastCheckTime = new Date();

      const duration = Date.now() - startTime;
      this.stats.averageCheckDuration =
        (this.stats.averageCheckDuration * (this.stats.totalChecks - 1) + duration) / this.stats.totalChecks;

      // 9. 显示性能统计
      const cacheStats = this.batchPoolService.getCacheStats();
      console.log(`\n🎯 超级并行化检查完成 (总耗时: ${duration}ms)`);
      console.log(`   - 数据预加载: ${preloadDuration}ms (${((preloadDuration / duration) * 100).toFixed(1)}%)`);
      console.log(
        `   - 路径分析: ${duration - preloadDuration}ms (${(((duration - preloadDuration) / duration) * 100).toFixed(1)}%)`,
      );
      console.log(`   - 发现机会: ${opportunities.length}`);
      console.log(`   - 总检查次数: ${this.stats.totalChecks}`);
      console.log(`   - 平均检查耗时: ${this.stats.averageCheckDuration.toFixed(0)}ms`);
      console.log(`   - 池子状态缓存: ${cacheStats.poolStateCache.size} 条目`);
      console.log(`   - 代币详情缓存: ${cacheStats.tokenDetailsCache.size} 条目`);

      return opportunities;
    } catch (error) {
      console.error('❌ 超级并行化套利检查失败:', error);
      return [];
    }
  }

  /**
   * 超级并行化检查三角套利机会（使用预加载的池子信息）
   */
  private async checkTriangularArbitrageSuperParallel(
    path: Address[],
    poolInfoMap: Map<Address, BatchPoolStateResult>,
  ): Promise<ArbitrageOpportunity | null> {
    if (path.length !== 4) {
      throw new Error('三角套利路径必须包含4个代币 (A->B->C->A)');
    }

    // 验证首尾代币相同
    if (path[0] !== path[3]) {
      throw new Error('三角套利路径的首尾代币必须相同');
    }

    const inputAmount = parseEther('1'); // 使用 1 个代币作为测试金额

    try {
      // 生成反向路径
      const reversePath = this.generateReversePath(path);

      console.log(
        `   🔄 并行检查: 正向 ${path
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')} | 反向 ${reversePath
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')}`,
      );

      // 并行检查正向和反向路径
      const [forwardResult, reverseResult] = await Promise.allSettled([
        this.checkSingleDirectionSuperParallel(path, inputAmount, '正向', poolInfoMap),
        this.checkSingleDirectionSuperParallel(reversePath, inputAmount, '反向', poolInfoMap),
      ]);

      // 提取结果
      const forwardOpportunity = forwardResult.status === 'fulfilled' ? forwardResult.value : null;
      const reverseOpportunity = reverseResult.status === 'fulfilled' ? reverseResult.value : null;

      // 记录错误（如果有）
      if (forwardResult.status === 'rejected') {
        console.log(`   ⚠️ 正向路径检查失败: ${forwardResult.reason}`);
      }
      if (reverseResult.status === 'rejected') {
        console.log(`   ⚠️ 反向路径检查失败: ${reverseResult.reason}`);
      }

      // 比较两个方向，选择更优的
      let bestOpportunity: ArbitrageOpportunity | null = null;

      if (forwardOpportunity && reverseOpportunity) {
        if (forwardOpportunity.estimatedProfitUSD > reverseOpportunity.estimatedProfitUSD) {
          bestOpportunity = forwardOpportunity;
          console.log(
            `   🏆 选择正向路径 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)} vs $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        } else {
          bestOpportunity = reverseOpportunity;
          console.log(
            `   🏆 选择反向路径 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)} vs $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        }
      } else if (forwardOpportunity) {
        bestOpportunity = forwardOpportunity;
        console.log(`   ✅ 仅正向路径可行 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else if (reverseOpportunity) {
        bestOpportunity = reverseOpportunity;
        console.log(`   ✅ 仅反向路径可行 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else {
        console.log('   ❌ 正向和反向路径都不可行');
      }

      return bestOpportunity;
    } catch (error) {
      console.log(`   ❌ 路径检查失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 超级并行化检查单个方向的套利机会（使用预加载的池子信息）
   */
  private async checkSingleDirectionSuperParallel(
    path: Address[],
    inputAmount: bigint,
    direction: string,
    poolInfoMap: Map<Address, BatchPoolStateResult>,
  ): Promise<ArbitrageOpportunity | null> {
    const [tokenA, tokenB, tokenC] = path; // 只取前3个代币

    try {
      // 预先确定所有三步的池子和方向
      const swapConfigs = this.getSwapConfigurations(tokenA, tokenB, tokenC);

      if (!swapConfigs) {
        console.log(`   ❌ ${direction}路径: 不支持的代币组合`);
        return null;
      }

      // 从预加载的数据中获取池子状态
      const step1PoolInfo = poolInfoMap.get(swapConfigs.step1.pool);
      const step2PoolInfo = poolInfoMap.get(swapConfigs.step2.pool);
      const step3PoolInfo = poolInfoMap.get(swapConfigs.step3.pool);

      if (!step1PoolInfo?.poolState || !step2PoolInfo?.poolState || !step3PoolInfo?.poolState) {
        console.log(`   ❌ ${direction}路径: 缺少必要的池子状态信息`);
        return null;
      }

      // 使用预加载的池子状态进行快速计算
      const step1Output = this.ultraFastSimulateSwapFromPoolState(
        step1PoolInfo.poolState,
        swapConfigs.step1.zeroForOne,
        inputAmount,
      );

      if (!step1Output) {
        console.log(`   ❌ ${direction}路径: 第一步模拟失败`);
        return null;
      }

      const step2Output = this.ultraFastSimulateSwapFromPoolState(
        step2PoolInfo.poolState,
        swapConfigs.step2.zeroForOne,
        step1Output,
      );

      if (!step2Output) {
        console.log(`   ❌ ${direction}路径: 第二步模拟失败`);
        return null;
      }

      const step3Output = this.ultraFastSimulateSwapFromPoolState(
        step3PoolInfo.poolState,
        swapConfigs.step3.zeroForOne,
        step2Output,
      );

      if (!step3Output) {
        console.log(`   ❌ ${direction}路径: 第三步模拟失败`);
        return null;
      }

      console.log(
        `   📊 ${direction}: ${formatEther(inputAmount)} → ${formatEther(step1Output)} → ${formatEther(step2Output)} → ${formatEther(step3Output)}`,
      );

      // 计算利润
      const profit = step3Output > inputAmount ? step3Output - inputAmount : BigInt(0);
      const profitPercentage = Number((profit * BigInt(10000)) / inputAmount) / 100;

      // 估算 Gas 费用和利润
      const estimatedGasCost = (BigInt(500000) * parseEther(this.config.gasPriceGwei.toString())) / BigInt(1e9);
      const estimatedGasCostUSD = Number(formatEther(estimatedGasCost)) * 600; // 假设 BNB = $600
      const estimatedProfitUSD = Number(formatEther(profit)) * 600;
      const netProfitUSD = estimatedProfitUSD - estimatedGasCostUSD;
      const profitable = netProfitUSD > this.config.minProfitThresholdUSD;

      console.log(
        `   💰 ${direction}利润: ${formatEther(profit)} (${profitPercentage.toFixed(4)}%) → $${netProfitUSD.toFixed(4)}`,
      );

      if (!profitable) {
        return null;
      }

      return {
        path,
        inputAmount,
        outputAmount: step3Output,
        profit,
        profitPercentage,
        estimatedGasCostUSD,
        estimatedProfitUSD: netProfitUSD,
        profitable,
        timestamp: new Date(),
        priceQueries: [
          {
            path: [tokenA, tokenB],
            inputAmount,
            outputAmount: step1Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenB, tokenC],
            inputAmount: step1Output,
            outputAmount: step2Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenC, tokenA],
            inputAmount: step2Output,
            outputAmount: step3Output,
            version: DexVersion.V3,
            fee: 0,
          },
        ],
      };
    } catch (error) {
      console.log(`   ❌ ${direction}路径价格查询失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 超快速模拟swap（使用BatchPoolStateService的PoolState数据）
   */
  private ultraFastSimulateSwapFromPoolState(
    poolState: import('./DirectV3PoolSwapService').PoolState,
    zeroForOne: boolean,
    amountSpecified: bigint,
  ): bigint | null {
    try {
      const sqrtPriceX96 = poolState.sqrtPriceX96;
      const Q96 = 2n ** 96n;
      const Q192 = Q96 * Q96;

      // 计算价格：price = (sqrtPriceX96)^2 / 2^192
      const priceX192 = sqrtPriceX96 * sqrtPriceX96;

      if (zeroForOne) {
        // token0 -> token1
        // outputAmount1 = inputAmount0 * price
        const outputAmount = -(amountSpecified * priceX192) / Q192;
        return outputAmount < 0n ? -outputAmount : outputAmount;
      }
      // token1 -> token0
      // outputAmount0 = inputAmount1 / price
      const outputAmount = -(amountSpecified * Q192) / priceX192;
      return outputAmount < 0n ? -outputAmount : outputAmount;
    } catch (error) {
      console.error('超快速模拟swap失败:', error);
      return null;
    }
  }

  /**
   * 超快速模拟swap（使用Multicall预加载的数据，零RPC调用）
   */
  private ultraFastSimulateSwap(
    poolData: MulticallPoolData,
    zeroForOne: boolean,
    amountSpecified: bigint,
  ): bigint | null {
    try {
      const sqrtPriceX96 = poolData.sqrtPriceX96;
      const Q96 = 2n ** 96n;
      const Q192 = Q96 * Q96;

      // 计算价格：price = (sqrtPriceX96)^2 / 2^192
      const priceX192 = sqrtPriceX96 * sqrtPriceX96;

      if (zeroForOne) {
        // token0 -> token1
        // outputAmount1 = inputAmount0 * price
        const outputAmount = -(amountSpecified * priceX192) / Q192;
        return outputAmount < 0n ? -outputAmount : outputAmount;
      }
      // token1 -> token0
      // outputAmount0 = inputAmount1 / price
      const outputAmount = -(amountSpecified * Q192) / priceX192;
      return outputAmount < 0n ? -outputAmount : outputAmount;
    } catch (error) {
      console.error('超快速模拟swap失败:', error);
      return null;
    }
  }

  /**
   * 为指定的三角套利路径添加所需的池子地址
   */
  private addPoolAddressesForPath(
    poolAddresses: Set<Address>,
    tokenA: Address,
    tokenB: Address,
    tokenC: Address,
  ): void {
    // 第一步: A -> B
    if (
      (tokenA === getTokenAddress('WBNB') && tokenB === getTokenAddress('BANK')) ||
      (tokenA === getTokenAddress('BANK') && tokenB === getTokenAddress('WBNB'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.BNB_BANK_POOL as Address);
    }
    if (
      (tokenA === getTokenAddress('WBNB') && tokenB === getTokenAddress('USD1')) ||
      (tokenA === getTokenAddress('USD1') && tokenB === getTokenAddress('WBNB'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.BNB_USD1_POOL as Address);
    }
    if (
      (tokenA === getTokenAddress('BANK') && tokenB === getTokenAddress('USD1')) ||
      (tokenA === getTokenAddress('USD1') && tokenB === getTokenAddress('BANK'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.USD1_BANK_POOL as Address);
    }

    // 第二步: B -> C
    if (
      (tokenB === getTokenAddress('WBNB') && tokenC === getTokenAddress('BANK')) ||
      (tokenB === getTokenAddress('BANK') && tokenC === getTokenAddress('WBNB'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.BNB_BANK_POOL as Address);
    }
    if (
      (tokenB === getTokenAddress('WBNB') && tokenC === getTokenAddress('USD1')) ||
      (tokenB === getTokenAddress('USD1') && tokenC === getTokenAddress('WBNB'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.BNB_USD1_POOL as Address);
    }
    if (
      (tokenB === getTokenAddress('BANK') && tokenC === getTokenAddress('USD1')) ||
      (tokenB === getTokenAddress('USD1') && tokenC === getTokenAddress('BANK'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.USD1_BANK_POOL as Address);
    }

    // 第三步: C -> A
    if (
      (tokenC === getTokenAddress('WBNB') && tokenA === getTokenAddress('BANK')) ||
      (tokenC === getTokenAddress('BANK') && tokenA === getTokenAddress('WBNB'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.BNB_BANK_POOL as Address);
    }
    if (
      (tokenC === getTokenAddress('WBNB') && tokenA === getTokenAddress('USD1')) ||
      (tokenC === getTokenAddress('USD1') && tokenA === getTokenAddress('WBNB'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.BNB_USD1_POOL as Address);
    }
    if (
      (tokenC === getTokenAddress('BANK') && tokenA === getTokenAddress('USD1')) ||
      (tokenC === getTokenAddress('USD1') && tokenA === getTokenAddress('BANK'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.USD1_BANK_POOL as Address);
    }
  }

  /**
   * 执行超级优化的套利检查（使用Multicall3，最少RPC调用）
   */
  async checkOnceUltraOptimized(): Promise<ArbitrageOpportunity[]> {
    const startTime = Date.now();
    console.log('\n🚀 开始超级优化套利机会检查（Multicall3）...');

    const opportunities: ArbitrageOpportunity[] = [];

    try {
      // 1. 预处理：收集所有需要的池子地址
      const allPoolAddresses = new Set<Address>();
      for (const path of this.config.paths) {
        const [tokenA, tokenB, tokenC] = path;

        // 为正向路径添加池子地址
        this.addPoolAddressesForPath(allPoolAddresses, tokenA, tokenB, tokenC);

        // 为反向路径添加池子地址
        const reversePath = this.generateReversePath(path);
        const [revTokenA, revTokenB, revTokenC] = reversePath;
        this.addPoolAddressesForPath(allPoolAddresses, revTokenA, revTokenB, revTokenC);
      }

      const poolAddressArray = Array.from(allPoolAddresses);
      console.log(`📊 预处理完成，需要查询 ${poolAddressArray.length} 个池子`);

      // 2. 使用Multicall批量预加载所有数据（最多2次RPC调用）
      const preloadStartTime = Date.now();
      const { pools, tokens } = await this.multicallService.batchGetCompleteInfo(poolAddressArray);
      const preloadDuration = Date.now() - preloadStartTime;

      console.log(`⚡ Multicall数据预加载完成，耗时 ${preloadDuration}ms`);
      console.log(`   - 成功获取池子: ${pools.size}/${poolAddressArray.length}`);
      console.log(`   - 成功获取代币: ${tokens.size} 个`);

      // 3. 并行检查所有路径
      const pathPromises = this.config.paths.map(async (path, index) => {
        const pathStartTime = Date.now();
        console.log(`📊 开始检查路径 ${index + 1}/${this.config.paths.length}: ${path.join(' -> ')}`);

        try {
          const opportunity = await this.checkTriangularArbitrageUltraOptimized(path, pools, tokens);
          const pathDuration = Date.now() - pathStartTime;

          if (opportunity?.profitable) {
            console.log(
              `✅ 路径 ${index + 1} 发现套利机会! 预期利润: $${opportunity.estimatedProfitUSD.toFixed(4)} (耗时: ${pathDuration}ms)`,
            );
            return opportunity;
          }
          console.log(`❌ 路径 ${index + 1} 无套利机会 (耗时: ${pathDuration}ms)`);
          return null;
        } catch (error) {
          const pathDuration = Date.now() - pathStartTime;
          console.log(
            `❌ 路径 ${index + 1} 检查失败: ${error instanceof Error ? error.message : String(error)} (耗时: ${pathDuration}ms)`,
          );
          return null;
        }
      });

      // 4. 等待所有路径检查完成
      const results = await Promise.allSettled(pathPromises);

      // 5. 收集成功的结果
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value) {
          opportunities.push(result.value);
        }
      }

      // 6. 更新统计信息
      this.stats.totalChecks++;
      this.stats.opportunitiesFound += opportunities.length;
      this.stats.lastCheckTime = new Date();

      const duration = Date.now() - startTime;
      this.stats.averageCheckDuration =
        (this.stats.averageCheckDuration * (this.stats.totalChecks - 1) + duration) / this.stats.totalChecks;

      // 7. 显示性能统计
      console.log(`\n🎯 超级优化检查完成 (总耗时: ${duration}ms)`);
      console.log(`   - Multicall预加载: ${preloadDuration}ms (${((preloadDuration / duration) * 100).toFixed(1)}%)`);
      console.log(
        `   - 路径分析: ${duration - preloadDuration}ms (${(((duration - preloadDuration) / duration) * 100).toFixed(1)}%)`,
      );
      console.log(`   - 发现机会: ${opportunities.length}`);
      console.log(`   - 总检查次数: ${this.stats.totalChecks}`);
      console.log(`   - 平均检查耗时: ${this.stats.averageCheckDuration.toFixed(0)}ms`);
      console.log('   - RPC调用次数: 最多2次 (vs 原来的27次)');

      return opportunities;
    } catch (error) {
      console.error('❌ 超级优化套利检查失败:', error);
      return [];
    }
  }

  /**
   * 超级优化检查三角套利机会（使用Multicall预加载的数据）
   */
  private async checkTriangularArbitrageUltraOptimized(
    path: Address[],
    pools: Map<Address, MulticallPoolData>,
    tokens: Map<Address, MulticallTokenData>,
  ): Promise<ArbitrageOpportunity | null> {
    if (path.length !== 4) {
      throw new Error('三角套利路径必须包含4个代币 (A->B->C->A)');
    }

    // 验证首尾代币相同
    if (path[0] !== path[3]) {
      throw new Error('三角套利路径的首尾代币必须相同');
    }

    const inputAmount = parseEther('1'); // 使用 1 个代币作为测试金额

    try {
      // 生成反向路径
      const reversePath = this.generateReversePath(path);

      console.log(
        `   🔄 并行检查: 正向 ${path
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')} | 反向 ${reversePath
          .slice(0, 3)
          .map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`)
          .join(' -> ')}`,
      );

      // 并行检查正向和反向路径
      const [forwardResult, reverseResult] = await Promise.allSettled([
        this.checkSingleDirectionUltraOptimized(path, inputAmount, '正向', pools, tokens),
        this.checkSingleDirectionUltraOptimized(reversePath, inputAmount, '反向', pools, tokens),
      ]);

      // 提取结果
      const forwardOpportunity = forwardResult.status === 'fulfilled' ? forwardResult.value : null;
      const reverseOpportunity = reverseResult.status === 'fulfilled' ? reverseResult.value : null;

      // 记录错误（如果有）
      if (forwardResult.status === 'rejected') {
        console.log(`   ⚠️ 正向路径检查失败: ${forwardResult.reason}`);
      }
      if (reverseResult.status === 'rejected') {
        console.log(`   ⚠️ 反向路径检查失败: ${reverseResult.reason}`);
      }

      // 比较两个方向，选择更优的
      let bestOpportunity: ArbitrageOpportunity | null = null;

      if (forwardOpportunity && reverseOpportunity) {
        if (forwardOpportunity.estimatedProfitUSD > reverseOpportunity.estimatedProfitUSD) {
          bestOpportunity = forwardOpportunity;
          console.log(
            `   🏆 选择正向路径 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)} vs $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        } else {
          bestOpportunity = reverseOpportunity;
          console.log(
            `   🏆 选择反向路径 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)} vs $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`,
          );
        }
      } else if (forwardOpportunity) {
        bestOpportunity = forwardOpportunity;
        console.log(`   ✅ 仅正向路径可行 (净利润: $${forwardOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else if (reverseOpportunity) {
        bestOpportunity = reverseOpportunity;
        console.log(`   ✅ 仅反向路径可行 (净利润: $${reverseOpportunity.estimatedProfitUSD.toFixed(4)})`);
      } else {
        console.log('   ❌ 正向和反向路径都不可行');
      }

      return bestOpportunity;
    } catch (error) {
      console.log(`   ❌ 路径检查失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 超级优化检查单个方向的套利机会（使用Multicall预加载的数据，零RPC调用）
   */
  private async checkSingleDirectionUltraOptimized(
    path: Address[],
    inputAmount: bigint,
    direction: string,
    pools: Map<Address, MulticallPoolData>,
    tokens: Map<Address, MulticallTokenData>,
  ): Promise<ArbitrageOpportunity | null> {
    const [tokenA, tokenB, tokenC] = path; // 只取前3个代币

    try {
      // 预先确定所有三步的池子和方向
      const swapConfigs = this.getSwapConfigurations(tokenA, tokenB, tokenC);

      if (!swapConfigs) {
        console.log(`   ❌ ${direction}路径: 不支持的代币组合`);
        return null;
      }

      // 从预加载的数据中获取池子状态
      const step1PoolData = pools.get(swapConfigs.step1.pool);
      const step2PoolData = pools.get(swapConfigs.step2.pool);
      const step3PoolData = pools.get(swapConfigs.step3.pool);

      if (!step1PoolData || !step2PoolData || !step3PoolData) {
        console.log(`   ❌ ${direction}路径: 缺少必要的池子状态信息`);
        return null;
      }

      // 使用预加载的池子状态进行快速计算（零RPC调用）
      const step1Output = this.ultraFastSimulateSwap(step1PoolData, swapConfigs.step1.zeroForOne, inputAmount);

      if (!step1Output) {
        console.log(`   ❌ ${direction}路径: 第一步模拟失败`);
        return null;
      }

      const step2Output = this.ultraFastSimulateSwap(step2PoolData, swapConfigs.step2.zeroForOne, step1Output);

      if (!step2Output) {
        console.log(`   ❌ ${direction}路径: 第二步模拟失败`);
        return null;
      }

      const step3Output = this.ultraFastSimulateSwap(step3PoolData, swapConfigs.step3.zeroForOne, step2Output);

      if (!step3Output) {
        console.log(`   ❌ ${direction}路径: 第三步模拟失败`);
        return null;
      }

      console.log(
        `   📊 ${direction}: ${formatEther(inputAmount)} → ${formatEther(step1Output)} → ${formatEther(step2Output)} → ${formatEther(step3Output)}`,
      );

      // 计算利润
      const profit = step3Output > inputAmount ? step3Output - inputAmount : BigInt(0);
      const profitPercentage = Number((profit * BigInt(10000)) / inputAmount) / 100;

      // 估算 Gas 费用和利润
      const estimatedGasCost = (BigInt(500000) * parseEther(this.config.gasPriceGwei.toString())) / BigInt(1e9);
      const estimatedGasCostUSD = Number(formatEther(estimatedGasCost)) * 600; // 假设 BNB = $600
      const estimatedProfitUSD = Number(formatEther(profit)) * 600;
      const netProfitUSD = estimatedProfitUSD - estimatedGasCostUSD;
      const profitable = netProfitUSD > this.config.minProfitThresholdUSD;

      console.log(
        `   💰 ${direction}利润: ${formatEther(profit)} (${profitPercentage.toFixed(4)}%) → $${netProfitUSD.toFixed(4)}`,
      );

      if (!profitable) {
        return null;
      }

      return {
        path,
        inputAmount,
        outputAmount: step3Output,
        profit,
        profitPercentage,
        estimatedGasCostUSD,
        estimatedProfitUSD: netProfitUSD,
        profitable,
        timestamp: new Date(),
        priceQueries: [
          {
            path: [tokenA, tokenB],
            inputAmount,
            outputAmount: step1Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenB, tokenC],
            inputAmount: step1Output,
            outputAmount: step2Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenC, tokenA],
            inputAmount: step2Output,
            outputAmount: step3Output,
            version: DexVersion.V3,
            fee: 0,
          },
        ],
      };
    } catch (error) {
      console.log(`   ❌ ${direction}路径价格查询失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }
}
