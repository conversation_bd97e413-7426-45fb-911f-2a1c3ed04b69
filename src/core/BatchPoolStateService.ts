import type { Address } from 'viem';
import { publicClient } from './BlockchainService';
import type { PoolState, TokenDetails } from './DirectV3PoolSwapService';

// V3 Pool ABI - 只包含需要的函数
const v3PoolAbi = [
  {
    inputs: [],
    name: 'token0',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'token1',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'fee',
    outputs: [{ internalType: 'uint24', name: '', type: 'uint24' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'liquidity',
    outputs: [{ internalType: 'uint128', name: '', type: 'uint128' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'slot0',
    outputs: [
      { internalType: 'uint160', name: 'sqrtPriceX96', type: 'uint160' },
      { internalType: 'int24', name: 'tick', type: 'int24' },
      { internalType: 'uint16', name: 'observationIndex', type: 'uint16' },
      { internalType: 'uint16', name: 'observationCardinality', type: 'uint16' },
      { internalType: 'uint16', name: 'observationCardinalityNext', type: 'uint16' },
      { internalType: 'uint32', name: 'feeProtocol', type: 'uint32' },
      { internalType: 'bool', name: 'unlocked', type: 'bool' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

// ERC20 ABI
const erc20Abi = [
  {
    inputs: [],
    name: 'decimals',
    outputs: [{ internalType: 'uint8', name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'symbol',
    outputs: [{ internalType: 'string', name: '', type: 'string' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

/**
 * 批量池子状态查询结果
 */
export interface BatchPoolStateResult {
  poolAddress: Address;
  poolState: PoolState | null;
  token0Details: TokenDetails | null;
  token1Details: TokenDetails | null;
  error?: string;
}

/**
 * 缓存条目
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

/**
 * 批量池子状态查询服务
 * 支持并行查询多个池子的完整状态信息，包含智能缓存
 */
export class BatchPoolStateService {
  private poolStateCache = new Map<Address, CacheEntry<PoolState>>();
  private tokenDetailsCache = new Map<Address, CacheEntry<TokenDetails>>();

  // 缓存TTL设置
  private readonly POOL_STATE_TTL = 2000; // 池子状态缓存2秒
  private readonly TOKEN_DETAILS_TTL = 300000; // 代币详情缓存5分钟

  /**
   * 检查缓存是否有效
   */
  private isCacheValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < entry.ttl;
  }

  /**
   * 获取缓存的池子状态
   */
  private getCachedPoolState(poolAddress: Address): PoolState | null {
    const entry = this.poolStateCache.get(poolAddress);
    if (entry && this.isCacheValid(entry)) {
      return entry.data;
    }
    return null;
  }

  /**
   * 缓存池子状态
   */
  private setCachedPoolState(poolAddress: Address, poolState: PoolState): void {
    this.poolStateCache.set(poolAddress, {
      data: poolState,
      timestamp: Date.now(),
      ttl: this.POOL_STATE_TTL,
    });
  }

  /**
   * 获取缓存的代币详情
   */
  private getCachedTokenDetails(tokenAddress: Address): TokenDetails | null {
    const entry = this.tokenDetailsCache.get(tokenAddress);
    if (entry && this.isCacheValid(entry)) {
      return entry.data;
    }
    return null;
  }

  /**
   * 缓存代币详情
   */
  private setCachedTokenDetails(tokenAddress: Address, tokenDetails: TokenDetails): void {
    this.tokenDetailsCache.set(tokenAddress, {
      data: tokenDetails,
      timestamp: Date.now(),
      ttl: this.TOKEN_DETAILS_TTL,
    });
  }

  /**
   * 批量获取池子状态
   */
  async batchGetPoolStates(poolAddresses: Address[]): Promise<Map<Address, PoolState>> {
    const results = new Map<Address, PoolState>();
    const uncachedPools: Address[] = [];

    // 检查缓存
    for (const poolAddress of poolAddresses) {
      const cached = this.getCachedPoolState(poolAddress);
      if (cached) {
        results.set(poolAddress, cached);
      } else {
        uncachedPools.push(poolAddress);
      }
    }

    if (uncachedPools.length === 0) {
      return results;
    }

    console.log(
      `🚀 批量查询 ${uncachedPools.length} 个池子状态 (${poolAddresses.length - uncachedPools.length} 个来自缓存)`,
    );
    const startTime = Date.now();

    // 并行查询未缓存的池子
    const promises = uncachedPools.map(async (poolAddress) => {
      try {
        const [token0, token1, fee, liquidity, slot0] = await Promise.all([
          publicClient.readContract({
            address: poolAddress,
            abi: v3PoolAbi,
            functionName: 'token0',
          }),
          publicClient.readContract({
            address: poolAddress,
            abi: v3PoolAbi,
            functionName: 'token1',
          }),
          publicClient.readContract({
            address: poolAddress,
            abi: v3PoolAbi,
            functionName: 'fee',
          }),
          publicClient.readContract({
            address: poolAddress,
            abi: v3PoolAbi,
            functionName: 'liquidity',
          }),
          publicClient.readContract({
            address: poolAddress,
            abi: v3PoolAbi,
            functionName: 'slot0',
          }),
        ]);

        const poolState: PoolState = {
          token0,
          token1,
          fee,
          liquidity,
          sqrtPriceX96: slot0[0],
          tick: slot0[1],
          unlocked: slot0[6],
        };

        // 缓存结果
        this.setCachedPoolState(poolAddress, poolState);
        return { poolAddress, poolState };
      } catch (error) {
        console.error(`获取池子状态失败 (${poolAddress}):`, error);
        return { poolAddress, poolState: null };
      }
    });

    const poolResults = await Promise.allSettled(promises);
    const duration = Date.now() - startTime;

    // 处理结果
    for (const result of poolResults) {
      if (result.status === 'fulfilled' && result.value.poolState) {
        results.set(result.value.poolAddress, result.value.poolState);
      }
    }

    console.log(`✅ 批量池子状态查询完成，耗时 ${duration}ms`);
    return results;
  }

  /**
   * 批量获取代币详情
   */
  async batchGetTokenDetails(tokenAddresses: Address[]): Promise<Map<Address, TokenDetails>> {
    const results = new Map<Address, TokenDetails>();
    const uncachedTokens: Address[] = [];

    // 检查缓存
    for (const tokenAddress of tokenAddresses) {
      const cached = this.getCachedTokenDetails(tokenAddress);
      if (cached) {
        results.set(tokenAddress, cached);
      } else {
        uncachedTokens.push(tokenAddress);
      }
    }

    if (uncachedTokens.length === 0) {
      return results;
    }

    console.log(
      `🚀 批量查询 ${uncachedTokens.length} 个代币详情 (${tokenAddresses.length - uncachedTokens.length} 个来自缓存)`,
    );
    const startTime = Date.now();

    // 并行查询未缓存的代币
    const promises = uncachedTokens.map(async (tokenAddress) => {
      try {
        const [decimals, symbol] = await Promise.all([
          publicClient.readContract({
            address: tokenAddress,
            abi: erc20Abi,
            functionName: 'decimals',
          }),
          publicClient.readContract({
            address: tokenAddress,
            abi: erc20Abi,
            functionName: 'symbol',
          }),
        ]);

        const tokenDetails: TokenDetails = {
          address: tokenAddress,
          symbol: symbol as string,
          decimals: decimals as number,
        };

        // 缓存结果
        this.setCachedTokenDetails(tokenAddress, tokenDetails);
        return { tokenAddress, tokenDetails };
      } catch (error) {
        console.error(`获取代币详情失败 (${tokenAddress}):`, error);
        return { tokenAddress, tokenDetails: null };
      }
    });

    const tokenResults = await Promise.allSettled(promises);
    const duration = Date.now() - startTime;

    // 处理结果
    for (const result of tokenResults) {
      if (result.status === 'fulfilled' && result.value.tokenDetails) {
        results.set(result.value.tokenAddress, result.value.tokenDetails);
      }
    }

    console.log(`✅ 批量代币详情查询完成，耗时 ${duration}ms`);
    return results;
  }

  /**
   * 批量获取完整的池子信息（包含代币详情）
   */
  async batchGetCompletePoolInfo(poolAddresses: Address[]): Promise<BatchPoolStateResult[]> {
    const startTime = Date.now();
    console.log(`🚀 批量获取 ${poolAddresses.length} 个池子的完整信息...`);

    // 1. 批量获取池子状态
    const poolStates = await this.batchGetPoolStates(poolAddresses);

    // 2. 收集所有需要查询的代币地址
    const tokenAddresses = new Set<Address>();
    for (const poolState of poolStates.values()) {
      tokenAddresses.add(poolState.token0);
      tokenAddresses.add(poolState.token1);
    }

    // 3. 批量获取代币详情
    const tokenDetails = await this.batchGetTokenDetails(Array.from(tokenAddresses));

    // 4. 组装结果
    const results: BatchPoolStateResult[] = poolAddresses.map((poolAddress) => {
      const poolState = poolStates.get(poolAddress);

      if (!poolState) {
        return {
          poolAddress,
          poolState: null,
          token0Details: null,
          token1Details: null,
          error: '无法获取池子状态',
        };
      }

      const token0Details = tokenDetails.get(poolState.token0);
      const token1Details = tokenDetails.get(poolState.token1);

      return {
        poolAddress,
        poolState,
        token0Details: token0Details || null,
        token1Details: token1Details || null,
      };
    });

    const duration = Date.now() - startTime;
    console.log(`✅ 批量完整池子信息查询完成，总耗时 ${duration}ms`);

    return results;
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache(): void {
    const now = Date.now();

    // 清理池子状态缓存
    for (const [key, entry] of this.poolStateCache.entries()) {
      if (!this.isCacheValid(entry)) {
        this.poolStateCache.delete(key);
      }
    }

    // 清理代币详情缓存
    for (const [key, entry] of this.tokenDetailsCache.entries()) {
      if (!this.isCacheValid(entry)) {
        this.tokenDetailsCache.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    poolStateCache: { size: number; hitRate: number };
    tokenDetailsCache: { size: number; hitRate: number };
  } {
    return {
      poolStateCache: {
        size: this.poolStateCache.size,
        hitRate: 0, // 可以添加命中率统计
      },
      tokenDetailsCache: {
        size: this.tokenDetailsCache.size,
        hitRate: 0, // 可以添加命中率统计
      },
    };
  }

  /**
   * 清空所有缓存
   */
  clearAllCache(): void {
    this.poolStateCache.clear();
    this.tokenDetailsCache.clear();
    console.log('已清空所有缓存');
  }
}
