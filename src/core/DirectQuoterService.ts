import { type Address, formatUnits, getAddress, parseEther } from 'viem';
import { PANCAKESWAP_V3_QUOTER_V2_ADDRESS } from '../constants/contracts';
import { BSC_TOKENS, type TokenSymbol, getTokenInfo } from '../constants/tokens';
import { publicClient } from './BlockchainService';

const iQuoterV2Abi = [
  {
    inputs: [
      {
        components: [
          { internalType: 'address', name: 'tokenIn', type: 'address' },
          { internalType: 'address', name: 'tokenOut', type: 'address' },
          { internalType: 'uint256', name: 'amountIn', type: 'uint256' },
          { internalType: 'uint24', name: 'fee', type: 'uint24' },
          { internalType: 'uint160', name: 'sqrtPriceLimitX96', type: 'uint160' },
        ],
        internalType: 'struct IQuoterV2.QuoteExactInputSingleParams',
        name: 'params',
        type: 'tuple',
      },
    ],
    name: 'quoteExactInputSingle',
    outputs: [
      { internalType: 'uint256', name: 'amountOut', type: 'uint256' },
      { internalType: 'uint160', name: 'sqrtPriceX96After', type: 'uint160' },
      { internalType: 'uint32', name: 'initializedTicksCrossed', type: 'uint32' },
      { internalType: 'uint256', name: 'gasEstimate', type: 'uint256' },
    ],
    stateMutability: 'nonpayable',
    type: 'function',
  },
] as const;

export interface QuoteExactInputSingleParams {
  tokenIn: Address;
  tokenOut: Address;
  amountIn: bigint;
  fee: number; // uint24
  sqrtPriceLimitX96: bigint; // uint160
}

export interface QuoteExactInputSingleResult {
  amountOut: bigint;
  sqrtPriceX96After: bigint;
  initializedTicksCrossed: number;
  gasEstimate: bigint;
}

export class DirectQuoterService {
  private quoterAddress: Address;

  constructor(quoterAddress: Address = PANCAKESWAP_V3_QUOTER_V2_ADDRESS) {
    this.quoterAddress = getAddress(quoterAddress); // Ensure checksummed address
  }

  async quoteSpecificPool(params: QuoteExactInputSingleParams): Promise<QuoteExactInputSingleResult | null> {
    try {
      const result: readonly [bigint, bigint, number, bigint] | unknown = await publicClient.readContract({
        address: this.quoterAddress,
        abi: iQuoterV2Abi,
        functionName: 'quoteExactInputSingle',
        args: [params],
      });

      if (Array.isArray(result) && result.length === 4) {
        return {
          amountOut: result[0] as bigint,
          sqrtPriceX96After: result[1] as bigint,
          initializedTicksCrossed: result[2] as number,
          gasEstimate: result[3] as bigint,
        };
      }
      console.error('Unexpected result format from quoteExactInputSingle:', result);
      return null;
    } catch (error) {
      console.error(`❌ 直接报价失败 for ${params.tokenIn} -> ${params.tokenOut} (fee: ${params.fee}):`, error);
      return null;
    }
  }
}

export async function testDirectQuoter(): Promise<void> {
  console.log('\n🧪 测试直接调用 PancakeSwap V3 QuoterV2...');
  const quoterService = new DirectQuoterService();

  const amountInSmall = parseEther('0.001');
  const amountInMedium = parseEther('1');

  // Helper to get token info or throw error
  const getSafeTokenInfo = (symbol: TokenSymbol) => {
    const info = getTokenInfo(symbol);
    if (!info) throw new Error(`Token info not found for ${symbol}`);
    return info;
  };

  const wbnbInfo = getSafeTokenInfo('WBNB');
  const bankInfo = getSafeTokenInfo('BANK');
  const usd1Info = getSafeTokenInfo('USD1');

  const testScenarios: (Omit<QuoteExactInputSingleParams, 'tokenIn' | 'tokenOut'> & {
    name: string;
    tokenInSymbol: TokenSymbol;
    tokenOutSymbol: TokenSymbol;
  })[] = [
    {
      name: 'WBNB -> BANK (0.01% fee) - Small Amount',
      tokenInSymbol: 'WBNB',
      tokenOutSymbol: 'BANK',
      amountIn: amountInSmall,
      fee: 100,
      sqrtPriceLimitX96: 0n,
    },
    {
      name: 'WBNB -> BANK (0.01% fee) - Medium Amount',
      tokenInSymbol: 'WBNB',
      tokenOutSymbol: 'BANK',
      amountIn: amountInMedium,
      fee: 100,
      sqrtPriceLimitX96: 0n,
    },
    {
      name: 'BANK -> WBNB (0.01% fee) - Small Amount',
      tokenInSymbol: 'BANK',
      tokenOutSymbol: 'WBNB',
      amountIn: amountInSmall,
      fee: 100,
      sqrtPriceLimitX96: 0n,
    },
    {
      name: 'BANK -> USD1 (0.01% fee) - Small Amount',
      tokenInSymbol: 'BANK',
      tokenOutSymbol: 'USD1',
      amountIn: amountInSmall,
      fee: 100,
      sqrtPriceLimitX96: 0n,
    },
    {
      name: 'USD1 -> BANK (0.01% fee) - Small Amount',
      tokenInSymbol: 'USD1',
      tokenOutSymbol: 'BANK',
      amountIn: amountInSmall,
      fee: 100,
      sqrtPriceLimitX96: 0n,
    },
    {
      name: 'WBNB -> USDT (0.05% fee) - Medium Amount', // Added from diagnostic results
      tokenInSymbol: 'WBNB',
      tokenOutSymbol: 'USDT',
      amountIn: amountInMedium,
      fee: 500, // From pool 0x36696169c63e42cd08ce11f5deebbcebae652050
      sqrtPriceLimitX96: 0n,
    },
    {
      name: 'WBNB -> USDT (0.01% fee) - Medium Amount', // Added from diagnostic results
      tokenInSymbol: 'WBNB',
      tokenOutSymbol: 'USDT',
      amountIn: amountInMedium,
      fee: 100, // From pool 0x172fcd41e0913e95784454622d1c3724f546f849
      sqrtPriceLimitX96: 0n,
    },
  ];

  for (const scenario of testScenarios) {
    console.log(`\n--- 报价场景: ${scenario.name} ---`);

    const tokenIn = getSafeTokenInfo(scenario.tokenInSymbol);
    const tokenOut = getSafeTokenInfo(scenario.tokenOutSymbol);

    console.log(`    路径: ${tokenIn.symbol} (${tokenIn.address}) -> ${tokenOut.symbol} (${tokenOut.address})`);
    console.log(`    金额进: ${formatUnits(scenario.amountIn, tokenIn.decimals)} ${tokenIn.symbol}`);
    console.log(`    费用: ${scenario.fee}`);

    const params: QuoteExactInputSingleParams = {
      tokenIn: tokenIn.address,
      tokenOut: tokenOut.address,
      amountIn: scenario.amountIn,
      fee: scenario.fee,
      sqrtPriceLimitX96: scenario.sqrtPriceLimitX96,
    };

    const result = await quoterService.quoteSpecificPool(params);

    if (result) {
      console.log('    ✅ 报价成功:');
      console.log(`       金额出: ${formatUnits(result.amountOut, tokenOut.decimals)} ${tokenOut.symbol}`);
      console.log(`       sqrtPriceX96After: ${result.sqrtPriceX96After}`);
      console.log(`       initializedTicksCrossed: ${result.initializedTicksCrossed}`);
      console.log(`       gasEstimate: ${result.gasEstimate}`);
    } else {
      console.log('    ❌ 报价失败或无结果。');
    }
  }
  console.log('\n✅ 直接报价测试完成。');
}
