import { type Address, parseAbiItem } from 'viem';
import { PANCAKESWAP_ROUTER_ADDRESS } from '../utils/config';
import { publicClient } from './BlockchainService';

// Manually defined ABI for the getAmountsOut function from IPancakeRouter02
const pancakeRouterAbi = [
  parseAbiItem(
    'function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)',
  ),
] as const; // Use 'as const' for stricter typing if Viem version supports it well, or define more explicitly

/**
 * Fetches the output amounts for a given input amount and token path from PancakeSwap.
 * @param amountIn The amount of the input token.
 * @param path An array of token addresses representing the swap path.
 * @returns A promise that resolves to an array of amounts, where the last element is the output amount.
 */
export async function getAmountsOut(amountIn: bigint, path: Address[]): Promise<bigint[]> {
  if (!PANCAKESWAP_ROUTER_ADDRESS) {
    throw new Error('PANCAKESWAP_ROUTER_ADDRESS is not configured in .env');
  }
  if (path.length < 2) {
    throw new Error('Path must contain at least two addresses.');
  }

  try {
    const data = await publicClient.readContract({
      address: PANCAKESWAP_ROUTER_ADDRESS as Address, // Cast to Address type
      abi: pancakeRouterAbi,
      functionName: 'getAmountsOut',
      args: [amountIn, path],
    });
    // The return type of readContract for this specific ABI item should be `readonly bigint[]` or `bigint[]`
    // Ensure the type matches what Viem returns or cast appropriately if necessary.
    return data as bigint[];
  } catch (error) {
    console.error(`Error fetching amounts out for path ${path.join(' -> ')}:`, error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

/**
 * 诊断代币路径问题的测试函数
 */
export async function diagnosePaths(): Promise<void> {
  const WBNB = '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c';
  const BANK = '0x3AeE7602b612de36088F3ffEd8c8f10E86EbF2bF';
  const USD1 = '0x8d0D000Ee44948FC98c9B98A4FA4921476f08B0d';
  const USDT = '0x55d398326f99059fF775485246999027B3197955';
  const BUSD = '0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56';

  const testAmount = 1000000000000000000n; // 1 token

  console.log('🔍 开始诊断代币路径...');
  console.log(`测试金额: ${testAmount} (1 token)`);
  console.log(`Router 地址: ${PANCAKESWAP_ROUTER_ADDRESS}`);
  console.log('');

  // 测试路径列表
  const testPaths = [
    { name: 'WBNB -> USDT', path: [WBNB, USDT] },
    { name: 'WBNB -> BUSD', path: [WBNB, BUSD] },
    { name: 'WBNB -> BANK (直接)', path: [WBNB, BANK] },
    { name: 'WBNB -> BANK (通过USDT)', path: [WBNB, USDT, BANK] },
    { name: 'WBNB -> BANK (通过BUSD)', path: [WBNB, BUSD, BANK] },
    { name: 'BANK -> USD1 (直接)', path: [BANK, USD1] },
    { name: 'BANK -> USD1 (通过USDT)', path: [BANK, USDT, USD1] },
    { name: 'BANK -> USD1 (通过WBNB)', path: [BANK, WBNB, USD1] },
    { name: 'USD1 -> WBNB (直接)', path: [USD1, WBNB] },
    { name: 'USD1 -> WBNB (通过USDT)', path: [USD1, USDT, WBNB] },
  ];

  for (const test of testPaths) {
    try {
      console.log(`📊 测试: ${test.name}`);
      console.log(`   路径: ${test.path.map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`).join(' -> ')}`);

      const amounts = await getAmountsOut(testAmount, test.path as Address[]);
      const outputAmount = amounts[amounts.length - 1];

      console.log(`   ✅ 成功! 输出: ${outputAmount}`);
      console.log(`   📈 兑换率: 1 -> ${Number(outputAmount) / Number(testAmount)}`);
    } catch (error) {
      console.log(`   ❌ 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
    console.log('');
  }
}

// 添加 Factory 合约 ABI 来检查流动性池
const factoryAbi = [
  parseAbiItem('function getPair(address tokenA, address tokenB) external view returns (address pair)'),
] as const;

/**
 * 检查两个代币之间是否存在流动性池
 */
export async function checkPairExists(tokenA: Address, tokenB: Address): Promise<string | null> {
  const FACTORY_ADDRESS = '0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73'; // PancakeSwap V2 Factory

  try {
    const pairAddress = await publicClient.readContract({
      address: FACTORY_ADDRESS,
      abi: factoryAbi,
      functionName: 'getPair',
      args: [tokenA, tokenB],
    });

    // 如果返回零地址，说明没有流动性池
    if (pairAddress === '0x0000000000000000000000000000000000000000') {
      return null;
    }

    return pairAddress as string;
  } catch (error) {
    console.error('检查流动性池失败:', error);
    return null;
  }
}

/**
 * 检查所有相关代币对的流动性池
 */
export async function checkAllPairs(): Promise<void> {
  const WBNB = '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c';
  const BANK = '0x3AeE7602b612de36088F3ffEd8c8f10E86EbF2bF';
  const USD1 = '0x8d0D000Ee44948FC98c9B98A4FA4921476f08B0d';
  const USDT = '0x55d398326f99059fF775485246999027B3197955';

  console.log('🔍 检查流动性池存在性...');

  const pairs = [
    { name: 'WBNB/BANK', tokenA: WBNB, tokenB: BANK },
    { name: 'BANK/USD1', tokenA: BANK, tokenB: USD1 },
    { name: 'USD1/WBNB', tokenA: USD1, tokenB: WBNB },
    { name: 'WBNB/USDT', tokenA: WBNB, tokenB: USDT },
    { name: 'BANK/USDT', tokenA: BANK, tokenB: USDT },
    { name: 'USD1/USDT', tokenA: USD1, tokenB: USDT },
  ];

  for (const pair of pairs) {
    const pairAddress = await checkPairExists(pair.tokenA as Address, pair.tokenB as Address);
    if (pairAddress) {
      console.log(`✅ ${pair.name}: ${pairAddress}`);
    } else {
      console.log(`❌ ${pair.name}: 不存在流动性池`);
    }
  }
}

/**
 * 检查 LP 地址的详细信息
 */
export async function checkLPInfo(lpAddress: Address): Promise<void> {
  console.log(`🔍 检查 LP 地址: ${lpAddress}`);

  // 检查是否是合约
  try {
    const code = await publicClient.getCode({ address: lpAddress });
    if (!code || code === '0x') {
      console.log('❌ 这不是一个合约地址');
      return;
    }
    console.log('✅ 确认是合约地址');
  } catch (error) {
    console.log('❌ 无法获取合约代码:', error);
    return;
  }

  // 尝试读取 LP 的 token0 和 token1
  const pairAbi = [
    parseAbiItem('function token0() external view returns (address)'),
    parseAbiItem('function token1() external view returns (address)'),
    parseAbiItem('function factory() external view returns (address)'),
    parseAbiItem(
      'function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
    ),
  ] as const;

  try {
    const [token0, token1, factory] = await Promise.all([
      publicClient.readContract({
        address: lpAddress,
        abi: pairAbi,
        functionName: 'token0',
      }),
      publicClient.readContract({
        address: lpAddress,
        abi: pairAbi,
        functionName: 'token1',
      }),
      publicClient.readContract({
        address: lpAddress,
        abi: pairAbi,
        functionName: 'factory',
      }),
    ]);

    console.log(`📊 Token0: ${token0}`);
    console.log(`📊 Token1: ${token1}`);
    console.log(`🏭 Factory: ${factory}`);

    // 检查是否是 PancakeSwap V2
    const PANCAKE_V2_FACTORY = '0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73';
    if (factory.toLowerCase() === PANCAKE_V2_FACTORY.toLowerCase()) {
      console.log('✅ 这是 PancakeSwap V2 的流动性池');
    } else {
      console.log(`⚠️  这不是 PancakeSwap V2 的流动性池，Factory: ${factory}`);
      console.log('   可能是其他 DEX 的流动性池');
    }

    // 获取储备量
    const reserves = await publicClient.readContract({
      address: lpAddress,
      abi: pairAbi,
      functionName: 'getReserves',
    });

    console.log(`💰 Reserve0: ${reserves[0]}`);
    console.log(`💰 Reserve1: ${reserves[1]}`);
    console.log(`⏰ Last Update: ${reserves[2]}`);
  } catch (error) {
    console.log('❌ 无法读取 LP 信息:', error);
  }
}

/**
 * 检查您提供的具体 LP 地址
 */
export async function checkYourLPs(): Promise<void> {
  console.log('🔍 检查您提供的 LP 地址...\n');

  const lpAddresses = [
    {
      name: 'BNB/BANK LP',
      address: '0xee6ff918a1f68b5d2fdecb14b367fa2eb5c6951c' as Address,
    },
    {
      name: 'USD1/BANK LP',
      address: '0x461f6989943a0820c12db66bd962d245b587eb3c' as Address,
    },
  ];

  for (const lp of lpAddresses) {
    console.log(`\n📋 ${lp.name}:`);
    await checkLPInfo(lp.address);
    console.log('');
  }
}

// Example usage (for testing purposes, normally called by ArbitrageFinder)
/*
async function testGetAmountsOut() {
  const WBNB = '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c'; // WBNB
  const BUSD = '0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56'; // BUSD
  const amountIn = 1000000000000000000n; // 1 WBNB

  try {
    const amounts = await getAmountsOut(amountIn, [WBNB, BUSD]);
    console.log(`For 1 WBNB, you can get ${amounts[1]} BUSD`);
  } catch (e) {
    console.error('Test failed:', e);
  }
}

testGetAmountsOut();
*/
