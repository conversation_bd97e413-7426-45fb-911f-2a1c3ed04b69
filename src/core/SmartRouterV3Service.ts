import { type Address, parseAbiItem } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { BSC_TOKENS, type TokenSymbol, getTokenAddress, getTokenSymbolByAddress } from '../constants/tokens';
import { publicClient } from './BlockchainService';

// Smart Router V3 ABI - 使用正确的 viem 格式
const smartRouterV3Abi = [
  // 用于查询的静态调用版本 - 简化版本
  parseAbiItem(
    'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)',
  ),
] as const;

/**
 * QuoteExactInputSingle 参数结构 (用于价格查询)
 */
export interface QuoteExactInputSingleParams {
  tokenIn: Address;
  tokenOut: Address;
  fee: number;
  amountIn: bigint;
  sqrtPriceLimitX96: bigint;
}

/**
 * PancakeSwap Smart Router V3 服务
 * 专门用于处理 BANK 代币等需要 Smart Router 的交易
 */
export class SmartRouterV3Service {
  private readonly routerAddress: Address;

  constructor() {
    this.routerAddress = BSC_CONTRACTS.PANCAKESWAP_SMART_ROUTER_V3 as Address;
  }

  /**
   * 使用 Smart Router V3 查询单路径交易的输出金额
   */
  async quoteExactInputSingle(
    tokenIn: Address,
    tokenOut: Address,
    fee: number,
    amountIn: bigint,
    sqrtPriceLimitX96 = 0n,
  ): Promise<bigint> {
    try {
      const result = await publicClient.readContract({
        address: this.routerAddress,
        abi: smartRouterV3Abi,
        functionName: 'quoteExactInputSingle',
        args: [tokenIn, tokenOut, fee, amountIn, sqrtPriceLimitX96],
      });

      return result;
    } catch (error) {
      console.error('Smart Router V3 quote failed:', error);
      throw error;
    }
  }

  /**
   * 测试不同手续费等级的 BANK 代币交易
   */
  async testBankTokenQuote(
    tokenIn: Address,
    tokenOut: Address,
    amountIn: bigint,
  ): Promise<{ fee: number; amountOut: bigint } | null> {
    // BANK 代币常用的手续费等级
    const fees = [500, 2500, 10000, 3000]; // 0.05%, 0.25%, 1%, 0.3%

    for (const fee of fees) {
      try {
        console.log(
          `   🔄 测试 Smart Router V3 (${fee / 100}%): ${tokenIn.slice(0, 6)}...${tokenIn.slice(-4)} -> ${tokenOut.slice(0, 6)}...${tokenOut.slice(-4)}`,
        );

        const amountOut = await this.quoteExactInputSingle(tokenIn, tokenOut, fee, amountIn);

        console.log(`   ✅ Smart Router V3 成功 (${fee / 100}%): ${amountOut}`);
        return { fee, amountOut };
      } catch (error) {
        console.log(
          `   ❌ Smart Router V3 失败 (${fee / 100}%): ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }

    return null;
  }

  /**
   * 获取当前时间戳 + 指定分钟数的 deadline
   */
  getDeadline(minutesFromNow = 10): bigint {
    return BigInt(Math.floor(Date.now() / 1000) + minutesFromNow * 60);
  }

  /**
   * 计算最小输出金额 (滑点保护)
   */
  calculateMinimumOut(expectedAmountOut: bigint, slippagePercent = 1): bigint {
    const slippageFactor = BigInt(Math.floor((100 - slippagePercent) * 100));
    return (expectedAmountOut * slippageFactor) / 10000n;
  }

  /**
   * 测试 BANK 代币交易 (简化版本)
   */
  async testBankTokenTrade(tokenInAddress: Address, tokenOutAddress: Address, amountIn: bigint): Promise<null> {
    const tokenInSymbol = getTokenSymbolByAddress(tokenInAddress) || tokenInAddress;
    const tokenOutSymbol = getTokenSymbolByAddress(tokenOutAddress) || tokenOutAddress;

    console.log(
      `\n📊 测试 Smart Router V3 (直接调用): ${tokenInSymbol} (${tokenInAddress.slice(0, 6)}...) -> ${tokenOutSymbol} (${tokenOutAddress.slice(0, 6)}...)`,
    );

    // Placeholder: Actual Smart Router V3 call would be here
    // For now, simulate based on previous findings that BANK has liquidity issues
    if (tokenInSymbol === 'BANK' || tokenOutSymbol === 'BANK') {
      console.log('❌ Smart Router V3: BANK 代币流动性不足 (模拟结果)');
      return null;
    }

    // Simulate a successful quote for other pairs (very basic)
    console.log('✅ Smart Router V3: 模拟报价成功 (非 BANK 代币)');
    // In a real scenario, this would return actual quote data
    return null;
  }
}

/**
 * 测试 PancakeSwap Smart Router V3 直接调用 (非 SDK)
 */
export async function testSmartRouterV3(): Promise<void> {
  console.log('🧪 测试 PancakeSwap Smart Router V3 (直接调用)...');
  console.log('');

  const service = new SmartRouterV3Service();

  const WBNB_ADDRESS = getTokenAddress('WBNB');
  const BANK_ADDRESS = getTokenAddress('BANK');
  const USD1_ADDRESS = getTokenAddress('USD1');
  const USDT_ADDRESS = getTokenAddress('USDT');

  const testAmount = 1000000000000000000n; // 1 token

  console.log('📋 测试 BANK 相关交易对:');
  const bankPairs = [
    { name: 'WBNB -> BANK', tokenIn: WBNB_ADDRESS, tokenOut: BANK_ADDRESS },
    { name: 'BANK -> USD1', tokenIn: BANK_ADDRESS, tokenOut: USD1_ADDRESS },
    { name: 'BANK -> USDT', tokenIn: BANK_ADDRESS, tokenOut: USDT_ADDRESS },
    { name: 'USD1 -> BANK', tokenIn: USD1_ADDRESS, tokenOut: BANK_ADDRESS },
  ];

  for (const pair of bankPairs) {
    await service.testBankTokenTrade(pair.tokenIn, pair.tokenOut, testAmount);
  }

  // Test a non-BANK pair
  console.log('\n📋 测试非 BANK 交易对:');
  await service.testBankTokenTrade(WBNB_ADDRESS, USDT_ADDRESS, testAmount);

  console.log('\n💡 结论: BANK 代币在 PancakeSwap Smart Router V3 上可能仍有流动性问题 (基于模拟)');
}

/**
 * 测试完整的三角套利路径 (使用 Smart Router V3 模拟)
 */
export async function testSmartRouterV3TriangularPath(): Promise<void> {
  console.log('\n🔺 测试 Smart Router V3 三角套利路径 (模拟)...');

  const service = new SmartRouterV3Service();

  const WBNB_ADDRESS = getTokenAddress('WBNB');
  const BANK_ADDRESS = getTokenAddress('BANK');
  const USD1_ADDRESS = getTokenAddress('USD1');

  const startAmount = 1000000000000000000n; // 1 WBNB

  console.log('\n🔄 测试路径: WBNB -> BANK -> USD1 -> WBNB');
  console.log(`起始金额: ${startAmount} (1 WBNB)`);

  // Step 1: WBNB -> BANK
  console.log('\n📊 Step 1: WBNB -> BANK');
  const step1Result = await service.testBankTokenTrade(WBNB_ADDRESS, BANK_ADDRESS, startAmount);
  if (step1Result === null) {
    // Assuming null means failure or no liquidity from our simulation
    console.log('❌ Step 1 失败 - BANK 代币流动性不足 (模拟结果)');
    return;
  }
  // const amountBank = step1Result.amountOut; // In a real scenario

  // Step 2: BANK -> USD1 (Example: would use amountBank from Step 1)
  // console.log('\n📊 Step 2: BANK -> USD1');
  // const step2Result = await service.testBankTokenTrade(BANK_ADDRESS, USD1_ADDRESS, amountBank);
  // if (step2Result === null) {
  //   console.log('❌ Step 2 失败 - BANK 或 USD1 流动性/路径问题 (模拟结果)');
  //   return;
  // }
  // const amountUsd1 = step2Result.amountOut;

  // Step 3: USD1 -> WBNB (Example: would use amountUsd1 from Step 2)
  // console.log('\n📊 Step 3: USD1 -> WBNB');
  // const step3Result = await service.testBankTokenTrade(USD1_ADDRESS, WBNB_ADDRESS, amountUsd1);
  // if (step3Result === null) {
  //   console.log('❌ Step 3 失败 - USD1 或 WBNB 流动性/路径问题 (模拟结果)');
  //   return;
  // }
  // const finalWbnbAmount = step3Result.amountOut;

  // console.log(`🏁 最终 WBNB 金额 (模拟): ${finalWbnbAmount}`);
  // console.log(`Profit/Loss: ${Number(finalWbnbAmount) - Number(startAmount)}`);

  console.log('❌ 由于 BANK 代币流动性问题，无法完成三角套利测试 (模拟结果)');
}
