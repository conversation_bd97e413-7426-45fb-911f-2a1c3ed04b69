import { type Address, parseAbiItem } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { publicClient } from './BlockchainService';

// V2 Router ABI
const v2RouterAbi = [
  parseAbiItem(
    'function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)',
  ),
] as const;

// V3 Quoter ABI
const v3QuoterAbi = [
  parseAbiItem(
    'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)',
  ),
] as const;

/**
 * 测试 V2 Router 价格查询
 */
async function testV2Router(tokenA: Address, tokenB: Address, amountIn: bigint): Promise<bigint | null> {
  try {
    console.log(
      `   🔄 测试 V2 Router: ${tokenA.slice(0, 6)}...${tokenA.slice(-4)} -> ${tokenB.slice(0, 6)}...${tokenB.slice(-4)}`,
    );

    const amounts = await publicClient.readContract({
      address: BSC_CONTRACTS.PANCAKESWAP_V2_ROUTER as Address,
      abi: v2RouterAbi,
      functionName: 'getAmountsOut',
      args: [amountIn, [tokenA, tokenB]],
    });

    const outputAmount = amounts[amounts.length - 1];
    console.log(`   ✅ V2 成功: ${outputAmount}`);
    return outputAmount;
  } catch (error) {
    console.log(`   ❌ V2 失败: ${error instanceof Error ? error.message : String(error)}`);
    return null;
  }
}

/**
 * 测试 V3 Quoter 价格查询
 */
async function testV3Quoter(tokenA: Address, tokenB: Address, amountIn: bigint): Promise<bigint | null> {
  const fees = [500, 2500, 10000]; // 0.05%, 0.25%, 1%

  for (const fee of fees) {
    try {
      console.log(
        `   🔄 测试 V3 Quoter (${fee / 100}%): ${tokenA.slice(0, 6)}...${tokenA.slice(-4)} -> ${tokenB.slice(0, 6)}...${tokenB.slice(-4)}`,
      );

      const outputAmount = await publicClient.readContract({
        address: BSC_CONTRACTS.PANCAKESWAP_V3_QUOTER as Address,
        abi: v3QuoterAbi,
        functionName: 'quoteExactInputSingle',
        args: [tokenA, tokenB, fee, amountIn, 0n],
      });

      console.log(`   ✅ V3 成功 (${fee / 100}%): ${outputAmount}`);
      return outputAmount;
    } catch (error) {
      console.log(`   ❌ V3 失败 (${fee / 100}%): ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  return null;
}

/**
 * 综合诊断不同 Router 的表现
 */
export async function diagnoseRouters(): Promise<void> {
  console.log('🔍 开始诊断不同 Router 的表现...');
  console.log(`V2 Router: ${BSC_CONTRACTS.PANCAKESWAP_V2_ROUTER}`);
  console.log(`V3 Quoter: ${BSC_CONTRACTS.PANCAKESWAP_V3_QUOTER}`);
  console.log('');

  // 测试代币
  const WBNB = '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c' as Address;
  const BANK = '0x3AeE7602b612de36088F3ffEd8c8f10E86EbF2bF' as Address;
  const USD1 = '0x8d0D000Ee44948FC98c9B98A4FA4921476f08B0d' as Address;
  const USDT = '0x55d398326f99059fF775485246999027B3197955' as Address;
  const BUSD = '0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56' as Address;

  const testAmount = 1000000000000000000n; // 1 token

  // 测试路径
  const testPairs = [
    { name: 'WBNB -> USDT', tokenA: WBNB, tokenB: USDT },
    { name: 'WBNB -> BUSD', tokenA: WBNB, tokenB: BUSD },
    { name: 'WBNB -> BANK', tokenA: WBNB, tokenB: BANK },
    { name: 'BANK -> USD1', tokenA: BANK, tokenB: USD1 },
    { name: 'USD1 -> WBNB', tokenA: USD1, tokenB: WBNB },
    { name: 'BANK -> USDT', tokenA: BANK, tokenB: USDT },
    { name: 'USD1 -> USDT', tokenA: USD1, tokenB: USDT },
  ];

  for (const pair of testPairs) {
    console.log(`\n📊 测试交易对: ${pair.name}`);

    // 测试 V2
    const v2Result = await testV2Router(pair.tokenA, pair.tokenB, testAmount);

    // 测试 V3
    const v3Result = await testV3Quoter(pair.tokenA, pair.tokenB, testAmount);

    // 比较结果
    if (v2Result && v3Result) {
      const v2Rate = Number(v2Result) / Number(testAmount);
      const v3Rate = Number(v3Result) / Number(testAmount);
      const difference = (Math.abs(v2Rate - v3Rate) / Math.max(v2Rate, v3Rate)) * 100;

      console.log(`   📈 V2 兑换率: 1 -> ${v2Rate.toFixed(6)}`);
      console.log(`   📈 V3 兑换率: 1 -> ${v3Rate.toFixed(6)}`);
      console.log(`   📊 价格差异: ${difference.toFixed(2)}%`);

      if (v3Rate > v2Rate) {
        console.log(`   🎯 V3 价格更优 (+${(((v3Rate - v2Rate) / v2Rate) * 100).toFixed(2)}%)`);
      } else if (v2Rate > v3Rate) {
        console.log(`   🎯 V2 价格更优 (+${(((v2Rate - v3Rate) / v3Rate) * 100).toFixed(2)}%)`);
      }
    } else if (v2Result) {
      console.log('   ✅ 仅 V2 可用');
    } else if (v3Result) {
      console.log('   ✅ 仅 V3 可用');
    } else {
      console.log('   ❌ V2 和 V3 都不可用');
    }
  }
}

/**
 * 测试三角套利路径在不同 Router 上的表现
 */
export async function testTriangularPaths(): Promise<void> {
  console.log('\n🔺 测试三角套利路径...');

  const WBNB = '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c' as Address;
  const BANK = '0x3AeE7602b612de36088F3ffEd8c8f10E86EbF2bF' as Address;
  const USD1 = '0x8d0D000Ee44948FC98c9B98A4FA4921476f08B0d' as Address;

  const startAmount = 1000000000000000000n; // 1 WBNB

  console.log('\n🔄 测试路径: WBNB -> BANK -> USD1 -> WBNB');
  console.log(`起始金额: ${startAmount} (1 WBNB)`);

  // V2 路径测试
  console.log('\n📊 V2 Router 测试:');
  try {
    // WBNB -> BANK
    const step1V2 = await testV2Router(WBNB, BANK, startAmount);
    if (!step1V2) {
      console.log('❌ V2: WBNB -> BANK 失败');
      console.log('❌ V2: BANK -> USD1 失败');
      console.log('❌ V2: USD1 -> WBNB 失败');
      return;
    }

    // BANK -> USD1
    const step2V2 = await testV2Router(BANK, USD1, step1V2);
    if (!step2V2) {
      console.log('❌ V2: BANK -> USD1 失败');
      return;
    }

    // USD1 -> WBNB
    const step3V2 = await testV2Router(USD1, WBNB, step2V2);
    if (!step3V2) {
      console.log('❌ V2: USD1 -> WBNB 失败');
      return;
    }

    const profitV2 = step3V2 - startAmount;
    const profitPercentV2 = (Number(profitV2) / Number(startAmount)) * 100;

    console.log('✅ V2 完整路径成功!');
    console.log(`   最终金额: ${step3V2}`);
    console.log(`   利润: ${profitV2} (${profitPercentV2.toFixed(4)}%)`);
  } catch (error) {
    console.log(`❌ V2 路径测试失败: ${error}`);
  }

  // V3 路径测试
  console.log('\n📊 V3 Quoter 测试:');
  try {
    // WBNB -> BANK
    const step1V3 = await testV3Quoter(WBNB, BANK, startAmount);
    if (!step1V3) {
      console.log('❌ V3: WBNB -> BANK 失败');
      return;
    }

    // BANK -> USD1
    const step2V3 = await testV3Quoter(BANK, USD1, step1V3);
    if (!step2V3) {
      console.log('❌ V3: BANK -> USD1 失败');
      return;
    }

    // USD1 -> WBNB
    const step3V3 = await testV3Quoter(USD1, WBNB, step2V3);
    if (!step3V3) {
      console.log('❌ V3: USD1 -> WBNB 失败');
      return;
    }

    const profitV3 = step3V3 - startAmount;
    const profitPercentV3 = (Number(profitV3) / Number(startAmount)) * 100;

    console.log('✅ V3 完整路径成功!');
    console.log(`   最终金额: ${step3V3}`);
    console.log(`   利润: ${profitV3} (${profitPercentV3.toFixed(4)}%)`);
  } catch (error) {
    console.log(`❌ V3 路径测试失败: ${error}`);
  }
}
