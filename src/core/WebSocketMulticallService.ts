import type { Address } from 'viem';
import { decodeFunctionResult, encodeFunctionData } from 'viem';
import { webSocketService } from './WebSocketService';

// V3 Pool ABI 片段
const v3PoolAbi = [
  {
    inputs: [],
    name: 'token0',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'token1',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'fee',
    outputs: [{ internalType: 'uint24', name: '', type: 'uint24' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'liquidity',
    outputs: [{ internalType: 'uint128', name: '', type: 'uint128' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'slot0',
    outputs: [
      { internalType: 'uint160', name: 'sqrtPriceX96', type: 'uint160' },
      { internalType: 'int24', name: 'tick', type: 'int24' },
      { internalType: 'uint16', name: 'observationIndex', type: 'uint16' },
      { internalType: 'uint16', name: 'observationCardinality', type: 'uint16' },
      { internalType: 'uint16', name: 'observationCardinalityNext', type: 'uint16' },
      { internalType: 'uint32', name: 'feeProtocol', type: 'uint32' },
      { internalType: 'bool', name: 'unlocked', type: 'bool' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

// ERC20 ABI 片段
const erc20Abi = [
  {
    inputs: [],
    name: 'decimals',
    outputs: [{ internalType: 'uint8', name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'symbol',
    outputs: [{ internalType: 'string', name: '', type: 'string' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

export interface WSMulticallPoolData {
  poolAddress: Address;
  token0: Address;
  token1: Address;
  fee: number;
  liquidity: bigint;
  sqrtPriceX96: bigint;
  tick: number;
  unlocked: boolean;
}

export interface WSMulticallTokenData {
  tokenAddress: Address;
  symbol: string;
  decimals: number;
}

/**
 * WebSocket版本的Multicall服务
 * 使用WebSocket连接进行更快的批量查询
 */
export class WebSocketMulticallService {
  /**
   * 确保WebSocket连接
   */
  private async ensureConnection(): Promise<void> {
    if (!webSocketService.isConnected()) {
      console.log('🔌 WebSocket未连接，正在建立连接...');
      await webSocketService.connect();
    }
  }

  /**
   * 批量获取池子状态（WebSocket版本）
   */
  async batchGetPoolStates(poolAddresses: Address[]): Promise<Map<Address, WSMulticallPoolData>> {
    const startTime = Date.now();
    console.log(`🚀 WebSocket批量查询 ${poolAddresses.length} 个池子状态...`);

    const results = new Map<Address, WSMulticallPoolData>();

    if (poolAddresses.length === 0) {
      return results;
    }

    try {
      await this.ensureConnection();

      // 构建所有调用
      const calls: Array<{
        address: Address;
        abi: typeof v3PoolAbi;
        functionName: string;
        args?: readonly unknown[];
      }> = [];

      // 为每个池子添加5个调用
      for (const poolAddress of poolAddresses) {
        calls.push(
          { address: poolAddress, abi: v3PoolAbi, functionName: 'token0' },
          { address: poolAddress, abi: v3PoolAbi, functionName: 'token1' },
          { address: poolAddress, abi: v3PoolAbi, functionName: 'fee' },
          { address: poolAddress, abi: v3PoolAbi, functionName: 'liquidity' },
          { address: poolAddress, abi: v3PoolAbi, functionName: 'slot0' },
        );
      }

      console.log(`   构建了 ${calls.length} 个调用 (${poolAddresses.length} 个池子 × 5)`);

      // 执行WebSocket批量查询
      const returnData = await webSocketService.batchReadContracts(calls);

      console.log('   WebSocket批量查询执行成功');

      // 解析结果
      for (let i = 0; i < poolAddresses.length; i++) {
        const poolAddress = poolAddresses[i];
        const baseIndex = i * 5;

        try {
          // 检查所有返回数据是否存在
          if (
            !returnData[baseIndex] ||
            !returnData[baseIndex + 1] ||
            !returnData[baseIndex + 2] ||
            !returnData[baseIndex + 3] ||
            !returnData[baseIndex + 4]
          ) {
            console.warn(`池子 ${poolAddress} 的数据不完整，跳过`);
            continue;
          }

          const token0 = returnData[baseIndex] as Address;
          const token1 = returnData[baseIndex + 1] as Address;
          const fee = returnData[baseIndex + 2] as number;
          const liquidity = returnData[baseIndex + 3] as bigint;
          const slot0 = returnData[baseIndex + 4] as readonly [bigint, number, number, number, number, number, boolean];

          results.set(poolAddress, {
            poolAddress,
            token0,
            token1,
            fee,
            liquidity,
            sqrtPriceX96: slot0[0],
            tick: slot0[1],
            unlocked: slot0[6],
          });
        } catch (error) {
          console.error(`解析池子 ${poolAddress} 数据失败:`, error);
        }
      }

      const duration = Date.now() - startTime;
      console.log(`✅ WebSocket批量池子查询完成，耗时 ${duration}ms (成功: ${results.size}/${poolAddresses.length})`);

      return results;
    } catch (error) {
      console.error('WebSocket批量池子查询失败:', error);
      return results;
    }
  }

  /**
   * 批量获取代币详情（WebSocket版本）
   */
  async batchGetTokenDetails(tokenAddresses: Address[]): Promise<Map<Address, WSMulticallTokenData>> {
    const startTime = Date.now();
    console.log(`🚀 WebSocket批量查询 ${tokenAddresses.length} 个代币详情...`);

    const results = new Map<Address, WSMulticallTokenData>();

    if (tokenAddresses.length === 0) {
      return results;
    }

    try {
      await this.ensureConnection();

      // 构建所有调用
      const calls: Array<{
        address: Address;
        abi: typeof erc20Abi;
        functionName: string;
        args?: readonly unknown[];
      }> = [];

      // 为每个代币添加2个调用
      for (const tokenAddress of tokenAddresses) {
        calls.push(
          { address: tokenAddress, abi: erc20Abi, functionName: 'decimals' },
          { address: tokenAddress, abi: erc20Abi, functionName: 'symbol' },
        );
      }

      console.log(`   构建了 ${calls.length} 个调用 (${tokenAddresses.length} 个代币 × 2)`);

      // 执行WebSocket批量查询
      const returnData = await webSocketService.batchReadContracts(calls);

      console.log('   WebSocket批量查询执行成功');

      // 解析结果
      for (let i = 0; i < tokenAddresses.length; i++) {
        const tokenAddress = tokenAddresses[i];
        const baseIndex = i * 2;

        try {
          // 检查返回数据是否存在
          if (!returnData[baseIndex] || !returnData[baseIndex + 1]) {
            console.warn(`代币 ${tokenAddress} 的数据不完整，跳过`);
            continue;
          }

          const decimals = returnData[baseIndex] as number;
          const symbol = returnData[baseIndex + 1] as string;

          results.set(tokenAddress, {
            tokenAddress,
            symbol,
            decimals,
          });
        } catch (error) {
          console.error(`解析代币 ${tokenAddress} 数据失败:`, error);
        }
      }

      const duration = Date.now() - startTime;
      console.log(`✅ WebSocket批量代币查询完成，耗时 ${duration}ms (成功: ${results.size}/${tokenAddresses.length})`);

      return results;
    } catch (error) {
      console.error('WebSocket批量代币查询失败:', error);
      return results;
    }
  }

  /**
   * 批量获取完整的池子和代币信息（WebSocket版本）
   */
  async batchGetCompleteInfo(poolAddresses: Address[]): Promise<{
    pools: Map<Address, WSMulticallPoolData>;
    tokens: Map<Address, WSMulticallTokenData>;
  }> {
    const startTime = Date.now();
    console.log(`🚀 WebSocket批量获取 ${poolAddresses.length} 个池子的完整信息...`);

    // 1. 批量获取池子状态
    const pools = await this.batchGetPoolStates(poolAddresses);

    // 2. 收集所有代币地址
    const tokenAddresses = new Set<Address>();
    for (const poolData of pools.values()) {
      tokenAddresses.add(poolData.token0);
      tokenAddresses.add(poolData.token1);
    }

    // 3. 批量获取代币详情
    const tokens = await this.batchGetTokenDetails(Array.from(tokenAddresses));

    const duration = Date.now() - startTime;
    console.log(`✅ WebSocket批量完整信息查询完成，总耗时 ${duration}ms`);
    console.log(`   - 池子: ${pools.size}/${poolAddresses.length}`);
    console.log(`   - 代币: ${tokens.size}/${tokenAddresses.size}`);

    return { pools, tokens };
  }
}
