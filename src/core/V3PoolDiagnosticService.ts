import { type Address, formatUnits, getAddress, parseAbiItem, parseEther } from 'viem';
import { publicClient } from './BlockchainService';

// PancakeSwap V3 Pool ABI (核心部分)
const v3PoolAbi = [
  parseAbiItem(
    'function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
  ),
  parseAbiItem('function liquidity() external view returns (uint128 liquidity)'),
  parseAbiItem('function fee() external view returns (uint24 fee)'),
  parseAbiItem('function token0() external view returns (address)'),
  parseAbiItem('function token1() external view returns (address)'),
] as const;

export interface V3PoolSlot0 {
  sqrtPriceX96: bigint;
  tick: number;
  observationIndex: number;
  observationCardinality: number;
  observationCardinalityNext: number;
  feeProtocol: number;
  unlocked: boolean;
}

export class V3PoolDiagnosticService {
  async getPoolSlot0(poolAddress: Address): Promise<V3PoolSlot0 | null> {
    try {
      const slot0DataArray = await publicClient.readContract({
        address: poolAddress,
        abi: v3PoolAbi,
        functionName: 'slot0',
      });
      return {
        sqrtPriceX96: slot0DataArray[0],
        tick: slot0DataArray[1],
        observationIndex: slot0DataArray[2],
        observationCardinality: slot0DataArray[3],
        observationCardinalityNext: slot0DataArray[4],
        feeProtocol: slot0DataArray[5],
        unlocked: slot0DataArray[6],
      };
    } catch (error) {
      console.error(`读取池 ${poolAddress} slot0 失败:`, error);
      return null;
    }
  }

  async getPoolLiquidity(poolAddress: Address): Promise<bigint | null> {
    try {
      const liquidity = await publicClient.readContract({
        address: poolAddress,
        abi: v3PoolAbi,
        functionName: 'liquidity',
      });
      return liquidity;
    } catch (error) {
      console.error(`读取池 ${poolAddress} 流动性失败:`, error);
      return null;
    }
  }

  async getPoolFee(poolAddress: Address): Promise<number | null> {
    try {
      const fee = await publicClient.readContract({
        address: poolAddress,
        abi: v3PoolAbi,
        functionName: 'fee',
      });
      return fee;
    } catch (error) {
      console.error(`读取池 ${poolAddress} 手续费失败:`, error);
      return null;
    }
  }

  async getPoolTokens(poolAddress: Address): Promise<{ token0: Address; token1: Address } | null> {
    try {
      const token0 = await publicClient.readContract({
        address: poolAddress,
        abi: v3PoolAbi,
        functionName: 'token0',
      });
      const token1 = await publicClient.readContract({
        address: poolAddress,
        abi: v3PoolAbi,
        functionName: 'token1',
      });
      return { token0, token1 };
    } catch (error) {
      console.error(`读取池 ${poolAddress} 代币地址失败:`, error);
      return null;
    }
  }

  async diagnosePool(poolAddress: Address): Promise<void> {
    console.log(`\n🔍 诊断 V3 池: ${poolAddress}`);
    const slot0 = await this.getPoolSlot0(poolAddress);
    const liquidity = await this.getPoolLiquidity(poolAddress);
    const fee = await this.getPoolFee(poolAddress);
    const tokens = await this.getPoolTokens(poolAddress);

    if (tokens) {
      console.log(`   代币0: ${tokens.token0}`);
      console.log(`   代币1: ${tokens.token1}`);
    } else {
      console.log('   ❌ 无法读取池代币地址。');
    }

    if (fee !== null) {
      console.log(`   手续费等级: ${fee} (${fee / 10000}%)`);
    } else {
      console.log('   ❌ 无法读取池手续费。');
    }

    if (slot0) {
      console.log('   池 Slot0 数据:');
      console.log(`     sqrtPriceX96: ${slot0.sqrtPriceX96}`);
      console.log(`     tick: ${slot0.tick}`);
    } else {
      console.log('   ❌ 无法读取池 Slot0 数据。');
    }

    if (liquidity !== null) {
      console.log(`   流动性: ${liquidity}`);
      if (liquidity === 0n) {
        console.log('   ⚠️ 警告: 该池当前流动性为 0！');
      }
    } else {
      console.log('   ❌ 无法读取池流动性。');
    }
  }
}

export async function testV3PoolDiagnostics(): Promise<void> {
  const service = new V3PoolDiagnosticService();

  const poolsToDiagnose: { name: string; address: Address }[] = [
    {
      name: 'BANK/WBNB (User Query)',
      address: getAddress('0xee6ff918a1f68b5d2fdecb14b367fa2eb5c6951c'),
    },
    {
      name: 'USDT/WBNB (0.05% - Known Active - Still failing diagnosis)',
      address: getAddress('0xcB517b137C17D75FB8931401855265F717386470'),
    },
    {
      name: 'BNB/USD1 (User Query)',
      address: getAddress('0x4a3218606af9b4728a9f187e1c1a8c07fbc172a9'),
    },
    {
      name: 'USD1/BSC-USD (Actual: USDT/USD1) (User Query)',
      address: getAddress('0x9c4ee895e4f6ce07ada631c508d1306db7502cce'),
    },
    {
      name: 'USDT/USD1 (User Query - Likely invalid)',
      address: getAddress('0x1e1dfff79d95725aaafd6b47af4fbc28d859ce28'),
    },
    {
      name: 'BNB/BANK (User Query - Likely invalid)',
      address: getAddress('0x185a1ff695d30a22c19f44c6b41e2d6d1c8c1f11'),
    },
    {
      name: 'USD1/BANK (User Query)',
      address: getAddress('0x461f6989943a0820c12db66bd962d245b587eb3c'),
    },
    // Newly added pools from user
    {
      name: 'BNB/BSC-USD (User Query 1)',
      address: getAddress('0x36696169c63e42cd08ce11f5deebbcebae652050'),
    },
    {
      name: 'BNB/BSC-USD (User Query 2)',
      address: getAddress('0x16b9a82891338f9ba80e2d6970fdda79d1eb0dae'),
    },
    {
      name: 'BNB/BSC-USD (User Query 3)',
      address: getAddress('0x172fcd41e0913e95784454622d1c3724f546f849'),
    },
  ];

  console.log('🧪 开始 V3 流动性池诊断测试...');

  for (const pool of poolsToDiagnose) {
    console.log(`\n--- 诊断池: ${pool.name} ---`);
    await service.diagnosePool(pool.address);
  }

  console.log('\n✅ V3 池诊断测试完成。');
}
