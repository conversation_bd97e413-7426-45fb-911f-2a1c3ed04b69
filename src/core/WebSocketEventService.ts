import type { Address } from 'viem';
import { webSocketService } from './WebSocketService';

/**
 * 池子状态变化事件
 */
export interface PoolStateChangeEvent {
  poolAddress: Address;
  blockNumber: bigint;
  sqrtPriceX96: bigint;
  liquidity: bigint;
  tick: number;
  timestamp: Date;
}

/**
 * 价格变化阈值配置
 */
export interface PriceChangeThreshold {
  poolAddress: Address;
  minPriceChangePercent: number; // 最小价格变化百分比
  maxUpdateInterval: number; // 最大更新间隔（毫秒）
}

/**
 * WebSocket事件订阅服务
 * 提供池子状态变化的实时监听和事件驱动更新
 */
export class WebSocketEventService {
  private subscriptions = new Map<
    Address,
    {
      threshold: PriceChangeThreshold;
      lastUpdate: Date;
      lastPrice: bigint;
      callbacks: Array<(event: PoolStateChangeEvent) => void>;
    }
  >();

  private isMonitoring = false;
  private monitoringInterval?: NodeJS.Timeout;
  private readonly MONITORING_INTERVAL = 1000; // 1秒检查一次

  /**
   * 订阅池子状态变化
   */
  subscribeToPoolChanges(threshold: PriceChangeThreshold, callback: (event: PoolStateChangeEvent) => void): void {
    const existing = this.subscriptions.get(threshold.poolAddress);

    if (existing) {
      // 添加新的回调到现有订阅
      existing.callbacks.push(callback);
      console.log(`📡 添加回调到现有池子订阅: ${threshold.poolAddress}`);
    } else {
      // 创建新的订阅
      this.subscriptions.set(threshold.poolAddress, {
        threshold,
        lastUpdate: new Date(0), // 初始时间设为很久以前
        lastPrice: 0n,
        callbacks: [callback],
      });
      console.log(`📡 创建新的池子订阅: ${threshold.poolAddress}`);
    }

    // 如果还没开始监控，启动监控
    if (!this.isMonitoring) {
      this.startMonitoring();
    }
  }

  /**
   * 取消订阅池子状态变化
   */
  unsubscribeFromPoolChanges(poolAddress: Address, callback?: (event: PoolStateChangeEvent) => void): void {
    const subscription = this.subscriptions.get(poolAddress);
    if (!subscription) {
      return;
    }

    if (callback) {
      // 移除特定回调
      const index = subscription.callbacks.indexOf(callback);
      if (index > -1) {
        subscription.callbacks.splice(index, 1);
      }

      // 如果没有回调了，移除整个订阅
      if (subscription.callbacks.length === 0) {
        this.subscriptions.delete(poolAddress);
        console.log(`📡 移除池子订阅: ${poolAddress}`);
      }
    } else {
      // 移除整个订阅
      this.subscriptions.delete(poolAddress);
      console.log(`📡 移除池子订阅: ${poolAddress}`);
    }

    // 如果没有订阅了，停止监控
    if (this.subscriptions.size === 0) {
      this.stopMonitoring();
    }
  }

  /**
   * 开始监控池子状态变化
   */
  private startMonitoring(): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    console.log(`📡 开始监控 ${this.subscriptions.size} 个池子的状态变化...`);

    this.monitoringInterval = setInterval(async () => {
      await this.checkPoolChanges();
    }, this.MONITORING_INTERVAL);
  }

  /**
   * 停止监控
   */
  private stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    console.log('📡 停止池子状态监控');
  }

  /**
   * 检查池子状态变化
   */
  private async checkPoolChanges(): Promise<void> {
    if (this.subscriptions.size === 0) {
      return;
    }

    try {
      // 确保WebSocket连接
      if (!webSocketService.isConnected()) {
        await webSocketService.connect();
      }

      const poolAddresses = Array.from(this.subscriptions.keys());
      const now = new Date();

      // 批量查询所有订阅的池子状态
      const calls = poolAddresses.flatMap((poolAddress) => [
        {
          address: poolAddress,
          abi: [
            {
              inputs: [],
              name: 'slot0',
              outputs: [
                { internalType: 'uint160', name: 'sqrtPriceX96', type: 'uint160' },
                { internalType: 'int24', name: 'tick', type: 'int24' },
                { internalType: 'uint16', name: 'observationIndex', type: 'uint16' },
                { internalType: 'uint16', name: 'observationCardinality', type: 'uint16' },
                { internalType: 'uint16', name: 'observationCardinalityNext', type: 'uint16' },
                { internalType: 'uint32', name: 'feeProtocol', type: 'uint32' },
                { internalType: 'bool', name: 'unlocked', type: 'bool' },
              ],
              stateMutability: 'view',
              type: 'function',
            },
          ] as const,
          functionName: 'slot0',
        },
        {
          address: poolAddress,
          abi: [
            {
              inputs: [],
              name: 'liquidity',
              outputs: [{ internalType: 'uint128', name: '', type: 'uint128' }],
              stateMutability: 'view',
              type: 'function',
            },
          ] as const,
          functionName: 'liquidity',
        },
      ]);

      const results = await webSocketService.batchReadContracts(calls);

      // 处理结果
      for (let i = 0; i < poolAddresses.length; i++) {
        const poolAddress = poolAddresses[i];
        const subscription = this.subscriptions.get(poolAddress);
        if (!subscription) continue;

        const slot0Index = i * 2;
        const liquidityIndex = i * 2 + 1;

        const slot0Result = results[slot0Index];
        const liquidityResult = results[liquidityIndex];

        if (!slot0Result || !liquidityResult) {
          console.warn(`⚠️ 池子 ${poolAddress} 数据获取失败`);
          continue;
        }

        const slot0 = slot0Result as readonly [bigint, number, number, number, number, number, boolean];
        const liquidity = liquidityResult as bigint;

        const currentPrice = slot0[0]; // sqrtPriceX96
        const tick = slot0[1];

        // 检查是否需要触发更新
        const shouldUpdate = this.shouldTriggerUpdate(subscription, currentPrice, now);

        if (shouldUpdate) {
          // 获取当前区块号
          const blockNumber = await webSocketService.getClient().getBlockNumber();

          const event: PoolStateChangeEvent = {
            poolAddress,
            blockNumber,
            sqrtPriceX96: currentPrice,
            liquidity,
            tick,
            timestamp: now,
          };

          // 更新订阅状态
          subscription.lastUpdate = now;
          subscription.lastPrice = currentPrice;

          // 触发所有回调
          for (const callback of subscription.callbacks) {
            try {
              callback(event);
            } catch (error) {
              console.error('❌ 池子状态变化回调执行失败:', error);
            }
          }

          console.log(`📡 池子状态变化事件: ${poolAddress.slice(0, 8)}... 价格: ${currentPrice}`);
        }
      }
    } catch (error) {
      console.error('❌ 检查池子状态变化失败:', error);
    }
  }

  /**
   * 判断是否应该触发更新
   */
  private shouldTriggerUpdate(
    subscription: {
      threshold: PriceChangeThreshold;
      lastUpdate: Date;
      lastPrice: bigint;
    },
    currentPrice: bigint,
    now: Date,
  ): boolean {
    const { threshold, lastUpdate, lastPrice } = subscription;

    // 如果是第一次更新
    if (lastPrice === 0n) {
      return true;
    }

    // 检查时间间隔
    const timeSinceLastUpdate = now.getTime() - lastUpdate.getTime();
    if (timeSinceLastUpdate >= threshold.maxUpdateInterval) {
      return true;
    }

    // 检查价格变化
    if (lastPrice > 0n) {
      const priceChange = currentPrice > lastPrice ? currentPrice - lastPrice : lastPrice - currentPrice;

      const priceChangePercent = Number((priceChange * 10000n) / lastPrice) / 100;

      if (priceChangePercent >= threshold.minPriceChangePercent) {
        return true;
      }
    }

    return false;
  }

  /**
   * 获取当前订阅状态
   */
  getSubscriptionStats(): {
    totalSubscriptions: number;
    poolAddresses: Address[];
    isMonitoring: boolean;
  } {
    return {
      totalSubscriptions: this.subscriptions.size,
      poolAddresses: Array.from(this.subscriptions.keys()),
      isMonitoring: this.isMonitoring,
    };
  }

  /**
   * 清理所有订阅
   */
  cleanup(): void {
    this.stopMonitoring();
    this.subscriptions.clear();
    console.log('📡 清理所有WebSocket事件订阅');
  }
}

// 全局WebSocket事件服务实例
export const webSocketEventService: WebSocketEventService = new WebSocketEventService();
