import { type Address, parseAbiItem } from 'viem';
import { publicClient } from './BlockchainService';

// PancakeSwap V3 Quoter 合约地址
const PANCAKESWAP_V3_QUOTER_ADDRESS = '0xB048Bbc1Ee6b733FFfCFb9e9CeF7375518e25997' as Address;

// PancakeSwap V3 Quoter ABI (只包含我们需要的函数)
const quoterAbi = [
  parseAbiItem(
    'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)',
  ),
  parseAbiItem('function quoteExactInput(bytes path, uint256 amountIn) external returns (uint256 amountOut)'),
] as const;

/**
 * 获取 PancakeSwap V3 的单跳价格
 * @param tokenIn 输入代币地址
 * @param tokenOut 输出代币地址
 * @param fee 手续费等级 (500, 2500, 10000)
 * @param amountIn 输入金额
 * @returns 输出金额
 */
export async function getV3QuoteExactInputSingle(
  tokenIn: Address,
  tokenOut: Address,
  fee: number,
  amountIn: bigint,
): Promise<bigint> {
  try {
    const result = await publicClient.readContract({
      address: PANCAKESWAP_V3_QUOTER_ADDRESS,
      abi: quoterAbi,
      functionName: 'quoteExactInputSingle',
      args: [tokenIn, tokenOut, fee, amountIn, BigInt(0)], // sqrtPriceLimitX96 = 0 表示无限制
    });

    return result;
  } catch (error) {
    console.error(`V3 Quote failed for ${tokenIn} -> ${tokenOut}:`, error);
    throw error;
  }
}

/**
 * 获取 PancakeSwap V3 的多跳价格
 * @param path 编码的路径 (token0 + fee + token1 + fee + token2...)
 * @param amountIn 输入金额
 * @returns 输出金额
 */
export async function getV3QuoteExactInput(path: `0x${string}`, amountIn: bigint): Promise<bigint> {
  try {
    const result = await publicClient.readContract({
      address: PANCAKESWAP_V3_QUOTER_ADDRESS,
      abi: quoterAbi,
      functionName: 'quoteExactInput',
      args: [path, amountIn],
    });

    return result;
  } catch (error) {
    console.error('V3 Multi-hop quote failed:', error);
    throw error;
  }
}

/**
 * 编码 V3 路径
 * @param tokens 代币地址数组
 * @param fees 手续费数组
 * @returns 编码的路径
 */
export function encodeV3Path(tokens: Address[], fees: number[]): `0x${string}` {
  if (tokens.length !== fees.length + 1) {
    throw new Error('Invalid path: tokens length should be fees length + 1');
  }

  let path = tokens[0].slice(2); // 移除 0x 前缀

  for (let i = 0; i < fees.length; i++) {
    // 添加手续费 (3 bytes)
    const feeHex = fees[i].toString(16).padStart(6, '0');
    path += feeHex;
    // 添加下一个代币地址
    path += tokens[i + 1].slice(2);
  }

  return `0x${path}` as `0x${string}`;
}

/**
 * 测试 BANK 和 USD1 代币的 V3 价格
 */
export async function testV3Prices(): Promise<void> {
  const BANK = '0x3AeE7602b612de36088F3ffEd8c8f10E86EbF2bF' as Address;
  const USD1 = '0x8d0D000Ee44948FC98c9B98A4FA4921476f08B0d' as Address;
  const WBNB = '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c' as Address;
  const USDT = '0x55d398326f99059fF775485246999027B3197955' as Address;

  const testAmount = BigInt('1000000000000000000'); // 1 token (18 decimals)

  console.log('🧪 测试 PancakeSwap V3 价格...\n');

  // 测试不同的手续费等级
  const fees = [500, 2500, 10000]; // 0.05%, 0.25%, 1%

  // 测试 BANK -> USD1 直接交易
  console.log('📊 测试 BANK -> USD1 (直接):');
  for (const fee of fees) {
    try {
      const result = await getV3QuoteExactInputSingle(BANK, USD1, fee, testAmount);
      console.log(`   手续费 ${fee / 10000}%: ${result.toString()} USD1`);
    } catch (error) {
      console.log(`   手续费 ${fee / 10000}%: ❌ 失败`);
    }
  }

  // 测试 USD1 -> BANK 直接交易
  console.log('\n📊 测试 USD1 -> BANK (直接):');
  for (const fee of fees) {
    try {
      const result = await getV3QuoteExactInputSingle(USD1, BANK, fee, testAmount);
      console.log(`   手续费 ${fee / 10000}%: ${result.toString()} BANK`);
    } catch (error) {
      console.log(`   手续费 ${fee / 10000}%: ❌ 失败`);
    }
  }

  // 测试多跳路径: BANK -> WBNB -> USD1
  console.log('\n📊 测试 BANK -> WBNB -> USD1 (多跳):');
  for (const fee of fees) {
    try {
      const path = encodeV3Path([BANK, WBNB, USD1], [fee, fee]);
      const result = await getV3QuoteExactInput(path, testAmount);
      console.log(`   手续费 ${fee / 10000}%: ${result.toString()} USD1`);
    } catch (error) {
      console.log(`   手续费 ${fee / 10000}%: ❌ 失败`);
    }
  }

  // 测试多跳路径: BANK -> USDT -> USD1
  console.log('\n📊 测试 BANK -> USDT -> USD1 (多跳):');
  for (const fee of fees) {
    try {
      const path = encodeV3Path([BANK, USDT, USD1], [fee, fee]);
      const result = await getV3QuoteExactInput(path, testAmount);
      console.log(`   手续费 ${fee / 10000}%: ${result.toString()} USD1`);
    } catch (error) {
      console.log(`   手续费 ${fee / 10000}%: ❌ 失败`);
    }
  }
}
