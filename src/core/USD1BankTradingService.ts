import type { Address } from 'viem';
import { formatEther, formatUnits, parseEther } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { getTokenAddress } from '../constants/tokens';
import { publicClient, walletClient } from './BlockchainService';
import { type DirectSwapParams, DirectV3PoolSwapService, type SwapResult } from './DirectV3PoolSwapService';

/**
 * USD1/BANK 交易建议
 */
export interface USD1BankTradingAdvice {
  direction: 'BANK_TO_USD1' | 'USD1_TO_BANK';
  inputAmount: bigint;
  inputSymbol: string;
  estimatedOutput: bigint;
  outputSymbol: string;
  exchangeRate: string;
  priceImpact: number;
  recommendation: 'SAFE' | 'CAUTION' | 'HIGH_RISK';
  warnings: string[];
  suggestedSlippage: number;
}

/**
 * USD1/BANK 专用交易服务
 * 处理极端价格比例的特殊情况
 */
export class USD1BankTradingService {
  private readonly poolAddress: Address = BSC_CONTRACTS.USD1_BANK_POOL as Address;
  private readonly directSwapService: DirectV3PoolSwapService;
  private readonly bankAddress: Address;
  private readonly usd1Address: Address;

  constructor() {
    this.directSwapService = new DirectV3PoolSwapService();
    this.bankAddress = getTokenAddress('BANK');
    this.usd1Address = getTokenAddress('USD1');
  }

  /**
   * 获取当前的 USD1/BANK 汇率（基于真实交易数据）
   */
  async getCurrentExchangeRate(): Promise<{
    bankToUsd1Rate: number;
    usd1ToBankRate: number;
    formattedRates: {
      bankToUsd1: string;
      usd1ToBank: string;
    };
  } | null> {
    try {
      // 使用小额测试来获取真实汇率
      const testAmount = parseEther('1'); // 1 token

      // 测试 BANK -> USD1
      const bankToUsd1Simulation = await this.directSwapService.simulateSwap(
        this.poolAddress,
        true, // zeroForOne: BANK -> USD1
        testAmount,
      );

      // 测试 USD1 -> BANK
      const usd1ToBankSimulation = await this.directSwapService.simulateSwap(
        this.poolAddress,
        false, // zeroForOne: USD1 -> BANK
        testAmount,
      );

      if (!bankToUsd1Simulation || !usd1ToBankSimulation) {
        return null;
      }

      // 计算真实汇率
      const bankToUsd1Output = Number(formatEther(-bankToUsd1Simulation.estimatedAmount1));
      const usd1ToBankOutput = Number(formatEther(-usd1ToBankSimulation.estimatedAmount0));

      const bankToUsd1Rate = bankToUsd1Output; // 1 BANK = ? USD1
      const usd1ToBankRate = usd1ToBankOutput; // 1 USD1 = ? BANK

      return {
        bankToUsd1Rate,
        usd1ToBankRate,
        formattedRates: {
          bankToUsd1: `1 BANK = ${bankToUsd1Rate.toFixed(6)} USD1`,
          usd1ToBank: `1 USD1 = ${usd1ToBankRate.toFixed(6)} BANK`,
        },
      };
    } catch (error) {
      console.error('获取汇率失败:', error);
      return null;
    }
  }

  /**
   * 分析交易并提供建议
   */
  async analyzeTrade(
    direction: 'BANK_TO_USD1' | 'USD1_TO_BANK',
    inputAmount: bigint,
  ): Promise<USD1BankTradingAdvice | null> {
    try {
      const isZeroForOne = direction === 'BANK_TO_USD1';

      // 模拟交易
      const simulation = await this.directSwapService.simulateSwap(this.poolAddress, isZeroForOne, inputAmount);

      if (!simulation) {
        return null;
      }

      const inputSymbol = direction === 'BANK_TO_USD1' ? 'BANK' : 'USD1';
      const outputSymbol = direction === 'BANK_TO_USD1' ? 'USD1' : 'BANK';

      const estimatedOutput = direction === 'BANK_TO_USD1' ? simulation.estimatedAmount1 : simulation.estimatedAmount0;

      // 计算汇率
      const inputFormatted = Number(formatEther(inputAmount));
      const outputFormatted = Number(formatEther(estimatedOutput < 0 ? -estimatedOutput : estimatedOutput));
      const exchangeRate = `${inputFormatted} ${inputSymbol} = ${outputFormatted.toExponential(6)} ${outputSymbol}`;

      // 风险评估
      const warnings: string[] = [];
      let recommendation: 'SAFE' | 'CAUTION' | 'HIGH_RISK' = 'SAFE';
      let suggestedSlippage = 1; // 默认 1%

      // 基于价格影响的风险评估
      if (simulation.priceImpact > 5) {
        recommendation = 'HIGH_RISK';
        warnings.push('价格影响超过 5%，可能导致较大损失');
        suggestedSlippage = 10;
      } else if (simulation.priceImpact > 1) {
        recommendation = 'CAUTION';
        warnings.push('价格影响超过 1%，建议谨慎交易');
        suggestedSlippage = 5;
      }

      // 基于交易金额的风险评估
      if (direction === 'BANK_TO_USD1') {
        const bankAmount = Number(formatEther(inputAmount));
        if (bankAmount > 1000) {
          recommendation = 'HIGH_RISK';
          warnings.push('BANK 交易金额较大，建议分批交易');
        } else if (bankAmount > 100) {
          recommendation = 'CAUTION';
          warnings.push('BANK 交易金额中等，建议监控价格影响');
        }
      } else {
        const usd1Amount = Number(formatEther(inputAmount));
        // 调整 USD1 的风险阈值，基于真实汇率 1 USD1 ≈ 12.98 BANK
        // 100亿 USD1 ≈ 1298 BANK，这是一个较大但合理的交易量
        if (usd1Amount > ********0000) {
          // 1000亿 USD1 (≈ 12980 BANK)
          recommendation = 'HIGH_RISK';
          warnings.push('USD1 交易金额极大，可能影响市场价格');
        } else if (usd1Amount > ********000) {
          // 100亿 USD1 (≈ 1298 BANK)
          recommendation = 'CAUTION';
          warnings.push('USD1 交易金额较大，建议监控价格影响');
        }
      }

      // 只在极大金额时才显示 USD1 特殊警告
      if (direction === 'USD1_TO_BANK' && Number(formatEther(inputAmount)) > ***********) {
        // 500亿 USD1
        warnings.push('USD1 交易金额极大，请确认交易金额正确');
      }

      return {
        direction,
        inputAmount,
        inputSymbol,
        estimatedOutput: estimatedOutput < 0 ? -estimatedOutput : estimatedOutput,
        outputSymbol,
        exchangeRate,
        priceImpact: simulation.priceImpact,
        recommendation,
        warnings,
        suggestedSlippage,
      };
    } catch (error) {
      console.error('分析交易失败:', error);
      return null;
    }
  }

  /**
   * 执行 USD1/BANK 交易
   */
  async executeTrade(
    direction: 'BANK_TO_USD1' | 'USD1_TO_BANK',
    inputAmount: bigint,
    slippageTolerance?: number,
  ): Promise<SwapResult> {
    try {
      // 先分析交易
      const analysis = await this.analyzeTrade(direction, inputAmount);

      if (!analysis) {
        return { success: false, error: '无法分析交易' };
      }

      // 检查风险级别
      if (analysis.recommendation === 'HIGH_RISK') {
        return {
          success: false,
          error: `高风险交易被阻止: ${analysis.warnings.join(', ')}`,
        };
      }

      if (!walletClient?.account) {
        return { success: false, error: '钱包未连接' };
      }

      // 使用建议的滑点或用户指定的滑点
      const finalSlippage = slippageTolerance || analysis.suggestedSlippage;

      const swapParams: DirectSwapParams = {
        poolAddress: this.poolAddress,
        recipient: walletClient.account.address,
        zeroForOne: direction === 'BANK_TO_USD1',
        amountSpecified: inputAmount,
        sqrtPriceLimitX96: 0n, // 无价格限制
        slippageTolerance: finalSlippage,
      };

      console.log(`🔄 执行 ${direction} 交易:`);
      console.log(`   输入: ${formatEther(inputAmount)} ${analysis.inputSymbol}`);
      console.log(`   预估输出: ${formatEther(analysis.estimatedOutput)} ${analysis.outputSymbol}`);
      console.log(`   价格影响: ${analysis.priceImpact.toFixed(4)}%`);
      console.log(`   滑点保护: ${finalSlippage}%`);
      console.log(`   风险级别: ${analysis.recommendation}`);

      if (analysis.warnings.length > 0) {
        console.log(`   ⚠️ 警告: ${analysis.warnings.join(', ')}`);
      }

      return await this.directSwapService.executeSwap(swapParams);
    } catch (error) {
      console.error('执行交易失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 获取安全的交易金额建议
   */
  getSafeAmountSuggestions(direction: 'BANK_TO_USD1' | 'USD1_TO_BANK'): {
    small: bigint;
    medium: bigint;
    large: bigint;
    descriptions: string[];
  } {
    if (direction === 'BANK_TO_USD1') {
      return {
        small: parseEther('1'), // 1 BANK
        medium: parseEther('10'), // 10 BANK
        large: parseEther('100'), // 100 BANK
        descriptions: [
          '小额测试: 1 BANK (约 0.077 USD1)',
          '中等金额: 10 BANK (约 0.77 USD1)',
          '大额交易: 100 BANK (约 7.7 USD1)',
        ],
      };
    }

    return {
      small: parseEther('1000000'), // 100万 USD1
      medium: parseEther('********0'), // 1亿 USD1
      large: parseEther('********000'), // 100亿 USD1
      descriptions: [
        '小额测试: 100万 USD1 (约 13 BANK)',
        '中等金额: 1亿 USD1 (约 1,298 BANK)',
        '大额交易: 100亿 USD1 (约 129,800 BANK)',
      ],
    };
  }
}

/**
 * 测试 USD1/BANK 专用交易服务
 */
export async function testUSD1BankTradingService(): Promise<void> {
  console.log('🏦 测试 USD1/BANK 专用交易服务...\n');

  const service = new USD1BankTradingService();

  // 1. 获取当前汇率
  console.log('💱 获取当前汇率:');
  const rates = await service.getCurrentExchangeRate();

  if (rates) {
    console.log(`   ${rates.formattedRates.bankToUsd1}`);
    console.log(`   ${rates.formattedRates.usd1ToBank}`);
  } else {
    console.log('❌ 无法获取汇率');
    return;
  }

  // 2. 测试交易分析 - BANK to USD1
  console.log('\n📊 测试 BANK -> USD1 交易分析:');
  const bankAmounts = [parseEther('1'), parseEther('10'), parseEther('100')];

  for (const amount of bankAmounts) {
    console.log(`\n--- 分析 ${formatEther(amount)} BANK -> USD1 ---`);
    const analysis = await service.analyzeTrade('BANK_TO_USD1', amount);

    if (analysis) {
      console.log(`   汇率: ${analysis.exchangeRate}`);
      console.log(`   价格影响: ${analysis.priceImpact.toFixed(4)}%`);
      console.log(`   风险级别: ${analysis.recommendation}`);
      console.log(`   建议滑点: ${analysis.suggestedSlippage}%`);
      if (analysis.warnings.length > 0) {
        console.log(`   ⚠️ 警告: ${analysis.warnings.join(', ')}`);
      }
    } else {
      console.log('❌ 分析失败');
    }
  }

  // 3. 测试交易分析 - USD1 to BANK
  console.log('\n📊 测试 USD1 -> BANK 交易分析:');
  const usd1Amounts = [parseEther('1000000'), parseEther('********'), parseEther('********0')];

  for (const amount of usd1Amounts) {
    console.log(`\n--- 分析 ${formatEther(amount)} USD1 -> BANK ---`);
    const analysis = await service.analyzeTrade('USD1_TO_BANK', amount);

    if (analysis) {
      console.log(`   汇率: ${analysis.exchangeRate}`);
      console.log(`   价格影响: ${analysis.priceImpact.toFixed(4)}%`);
      console.log(`   风险级别: ${analysis.recommendation}`);
      console.log(`   建议滑点: ${analysis.suggestedSlippage}%`);
      if (analysis.warnings.length > 0) {
        console.log(`   ⚠️ 警告: ${analysis.warnings.join(', ')}`);
      }
    } else {
      console.log('❌ 分析失败');
    }
  }

  // 4. 显示安全金额建议
  console.log('\n💡 安全交易金额建议:');

  console.log('\n🔄 BANK -> USD1:');
  const bankSuggestions = service.getSafeAmountSuggestions('BANK_TO_USD1');
  for (const desc of bankSuggestions.descriptions) {
    console.log(`   ${desc}`);
  }

  console.log('\n🔄 USD1 -> BANK:');
  const usd1Suggestions = service.getSafeAmountSuggestions('USD1_TO_BANK');
  for (const desc of usd1Suggestions.descriptions) {
    console.log(`   ${desc}`);
  }

  // 5. 重要提示
  console.log('\n⚠️ 重要提示:');
  console.log('   - USD1 相对于 BANK 价值较低');
  console.log('   - 1 BANK ≈ 0.077 USD1');
  console.log('   - 1 USD1 ≈ 12.98 BANK');
  console.log('   - 建议根据价格影响评估风险');
  console.log('   - 注意滑点设置');
  console.log('   - 极高风险交易会被自动阻止');

  console.log('\n✅ USD1/BANK 专用交易服务测试完成');
}
