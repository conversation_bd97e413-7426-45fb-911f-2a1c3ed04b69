import { type Address, parseAbiItem } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { publicClient } from './BlockchainService';

// 导入现有的 V2 和 V3 服务
import { getAmountsOut as getV2AmountsOut } from './DexService';
import { encodeV3Path, getV3QuoteExactInput, getV3QuoteExactInputSingle } from './DexServiceV3';

// DEX 版本枚举
export enum DexVersion {
  V2 = 'V2',
  V3 = 'V3',
}

// 价格查询结果接口
export interface PriceQuote {
  version: DexVersion;
  outputAmount: bigint;
  path: Address[];
  fee?: number; // V3 专用
  gasEstimate?: bigint;
  priceImpact?: number;
}

// 路径配置接口
export interface PathConfig {
  tokens: Address[];
  v2Available: boolean;
  v3Available: boolean;
  v3Fees?: number[]; // V3 手续费等级
}

/**
 * 统一的 DEX 服务类，同时支持 V2 和 V3
 * 使用官方最新合约地址: https://developer.pancakeswap.finance/contracts/universal-router/addresses
 */
export class UnifiedDexService {
  private readonly v3Quoter = BSC_CONTRACTS.PANCAKESWAP_V3_QUOTER as Address;
  private readonly v2Factory = BSC_CONTRACTS.PANCAKESWAP_V2_FACTORY as Address;
  private readonly v3Factory = BSC_CONTRACTS.PANCAKESWAP_V3_FACTORY as Address;

  /**
   * 获取最佳价格报价（同时查询 V2 和 V3）
   */
  async getBestQuote(
    amountIn: bigint,
    path: Address[],
    options?: {
      includeV2?: boolean;
      includeV3?: boolean;
      v3Fees?: number[];
    },
  ): Promise<PriceQuote | null> {
    const { includeV2 = true, includeV3 = true, v3Fees = [500, 2500, 10000] } = options || {};

    const quotes: PriceQuote[] = [];

    // 查询 V2 价格
    if (includeV2) {
      try {
        const v2Quote = await this.getV2Quote(amountIn, path);
        if (v2Quote) {
          quotes.push(v2Quote);
        }
      } catch (error) {
        console.log(`V2 查询失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // 查询 V3 价格（多个手续费等级）
    if (includeV3) {
      const v3Quotes = await this.getV3Quotes(amountIn, path, v3Fees);
      quotes.push(...v3Quotes);
    }

    // 返回输出金额最大的报价
    if (quotes.length === 0) {
      return null;
    }

    return quotes.reduce((best, current) => (current.outputAmount > best.outputAmount ? current : best));
  }

  /**
   * 获取 V2 价格报价
   */
  private async getV2Quote(amountIn: bigint, path: Address[]): Promise<PriceQuote | null> {
    try {
      const amounts = await getV2AmountsOut(amountIn, path);
      return {
        version: DexVersion.V2,
        outputAmount: amounts[amounts.length - 1],
        path,
      };
    } catch (error) {
      console.log(`V2 路径 ${path.join(' -> ')} 查询失败`);
      return null;
    }
  }

  /**
   * 获取 V3 价格报价（多个手续费等级）
   */
  private async getV3Quotes(amountIn: bigint, path: Address[], fees: number[]): Promise<PriceQuote[]> {
    const quotes: PriceQuote[] = [];

    // 直接交易（两个代币）
    if (path.length === 2) {
      for (const fee of fees) {
        try {
          const outputAmount = await getV3QuoteExactInputSingle(path[0], path[1], fee, amountIn);
          quotes.push({
            version: DexVersion.V3,
            outputAmount,
            path,
            fee,
          });
        } catch (error) {
          // 忽略失败的手续费等级
        }
      }
    }
    // 多跳交易
    else if (path.length > 2) {
      for (const fee of fees) {
        try {
          // 为多跳路径使用相同的手续费
          const feePath = new Array(path.length - 1).fill(fee);
          const encodedPath = encodeV3Path(path, feePath);
          const outputAmount = await getV3QuoteExactInput(encodedPath, amountIn);

          quotes.push({
            version: DexVersion.V3,
            outputAmount,
            path,
            fee,
          });
        } catch (error) {
          // 忽略失败的手续费等级
        }
      }
    }

    return quotes;
  }

  /**
   * 检查路径在不同版本中的可用性
   */
  async checkPathAvailability(path: Address[]): Promise<PathConfig> {
    const config: PathConfig = {
      tokens: path,
      v2Available: false,
      v3Available: false,
      v3Fees: [],
    };

    // 检查 V2 可用性
    try {
      await getV2AmountsOut(BigInt('1000000000000000000'), path);
      config.v2Available = true;
    } catch (error) {
      // V2 不可用
    }

    // 检查 V3 可用性（不同手续费等级）
    const availableFees: number[] = [];
    const fees = [500, 2500, 10000];

    if (path.length === 2) {
      // 直接交易
      for (const fee of fees) {
        try {
          await getV3QuoteExactInputSingle(path[0], path[1], fee, BigInt('1000000000000000000'));
          availableFees.push(fee);
        } catch (error) {
          // 该手续费等级不可用
        }
      }
    } else if (path.length > 2) {
      // 多跳交易
      for (const fee of fees) {
        try {
          const feePath = new Array(path.length - 1).fill(fee);
          const encodedPath = encodeV3Path(path, feePath);
          await getV3QuoteExactInput(encodedPath, BigInt('1000000000000000000'));
          availableFees.push(fee);
        } catch (error) {
          // 该手续费等级不可用
        }
      }
    }

    if (availableFees.length > 0) {
      config.v3Available = true;
      config.v3Fees = availableFees;
    }

    return config;
  }

  /**
   * 比较所有可能的路径并返回最佳选择
   */
  async findBestPath(
    amountIn: bigint,
    tokenA: Address,
    tokenB: Address,
    intermediateTokens: Address[] = [],
  ): Promise<PriceQuote | null> {
    const allPaths: Address[][] = [];

    // 直接路径
    allPaths.push([tokenA, tokenB]);

    // 通过中间代币的路径
    for (const intermediate of intermediateTokens) {
      allPaths.push([tokenA, intermediate, tokenB]);
    }

    const quotes: PriceQuote[] = [];

    // 测试所有路径
    for (const path of allPaths) {
      const quote = await this.getBestQuote(amountIn, path);
      if (quote) {
        quotes.push(quote);
      }
    }

    // 返回最佳报价
    if (quotes.length === 0) {
      return null;
    }

    return quotes.reduce((best, current) => (current.outputAmount > best.outputAmount ? current : best));
  }

  /**
   * 获取三角套利的最佳路径组合
   */
  async getTriangularArbitragePaths(
    tokenA: Address,
    tokenB: Address,
    tokenC: Address,
    amountIn: bigint,
  ): Promise<{
    path1: PriceQuote | null; // A -> B
    path2: PriceQuote | null; // B -> C
    path3: PriceQuote | null; // C -> A
    totalOutput: bigint;
    profit: bigint;
    profitable: boolean;
  }> {
    // 第一步：A -> B
    const path1 = await this.getBestQuote(amountIn, [tokenA, tokenB]);
    if (!path1) {
      return {
        path1: null,
        path2: null,
        path3: null,
        totalOutput: BigInt(0),
        profit: BigInt(0),
        profitable: false,
      };
    }

    // 第二步：B -> C
    const path2 = await this.getBestQuote(path1.outputAmount, [tokenB, tokenC]);
    if (!path2) {
      return {
        path1,
        path2: null,
        path3: null,
        totalOutput: BigInt(0),
        profit: BigInt(0),
        profitable: false,
      };
    }

    // 第三步：C -> A
    const path3 = await this.getBestQuote(path2.outputAmount, [tokenC, tokenA]);
    if (!path3) {
      return {
        path1,
        path2,
        path3: null,
        totalOutput: BigInt(0),
        profit: BigInt(0),
        profitable: false,
      };
    }

    const totalOutput = path3.outputAmount;
    const profit = totalOutput > amountIn ? totalOutput - amountIn : BigInt(0);
    const profitable = profit > BigInt(0);

    return {
      path1,
      path2,
      path3,
      totalOutput,
      profit,
      profitable,
    };
  }
}

/**
 * 测试统一 DexService 功能
 */
export async function testUnifiedDexService(): Promise<void> {
  const dexService = new UnifiedDexService();

  const BANK = '0x3AeE7602b612de36088F3ffEd8c8f10E86EbF2bF' as Address;
  const USD1 = '0x8d0D000Ee44948FC98c9B98A4FA4921476f08B0d' as Address;
  const WBNB = '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c' as Address;
  const USDT = '0x55d398326f99059fF775485246999027B3197955' as Address;
  const BUSD = '0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56' as Address;

  const testAmount = BigInt('1000000000000000000'); // 1 token

  console.log('🧪 测试统一 DexService...\n');

  // 测试路径可用性检查
  console.log('📋 检查路径可用性:');
  const testPaths = [
    [USDT, BUSD],
    [WBNB, USDT],
    [BANK, USD1],
    [BANK, WBNB, USD1],
  ];

  for (const path of testPaths) {
    const config = await dexService.checkPathAvailability(path as Address[]);
    const pathStr = path.map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`).join(' -> ');
    console.log(`   ${pathStr}:`);
    console.log(`     V2: ${config.v2Available ? '✅' : '❌'}`);
    console.log(
      `     V3: ${config.v3Available ? '✅' : '❌'} ${config.v3Fees?.length ? `(费率: ${config.v3Fees.join(', ')})` : ''}`,
    );
  }

  // 测试最佳价格查询
  console.log('\n💰 最佳价格查询:');

  // 测试 USDT -> BUSD（应该在 V2 有流动性）
  console.log('\n📊 USDT -> BUSD:');
  const usdtBusdQuote = await dexService.getBestQuote(testAmount, [USDT, BUSD]);
  if (usdtBusdQuote) {
    console.log(`   最佳报价: ${usdtBusdQuote.version} - ${usdtBusdQuote.outputAmount.toString()}`);
    if (usdtBusdQuote.fee) {
      console.log(`   手续费: ${usdtBusdQuote.fee / 10000}%`);
    }
  } else {
    console.log('   ❌ 无可用报价');
  }

  // 测试 BANK -> USD1
  console.log('\n📊 BANK -> USD1:');
  const bankUsd1Quote = await dexService.getBestQuote(testAmount, [BANK, USD1]);
  if (bankUsd1Quote) {
    console.log(`   最佳报价: ${bankUsd1Quote.version} - ${bankUsd1Quote.outputAmount.toString()}`);
    if (bankUsd1Quote.fee) {
      console.log(`   手续费: ${bankUsd1Quote.fee / 10000}%`);
    }
  } else {
    console.log('   ❌ 无可用报价');
  }

  // 测试最佳路径查找
  console.log('\n🛣️ 最佳路径查找 (BANK -> USD1):');
  const bestPath = await dexService.findBestPath(
    testAmount,
    BANK,
    USD1,
    [WBNB, USDT, BUSD], // 中间代币
  );

  if (bestPath) {
    const pathStr = bestPath.path.map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`).join(' -> ');
    console.log(`   最佳路径: ${pathStr}`);
    console.log(`   版本: ${bestPath.version}`);
    console.log(`   输出: ${bestPath.outputAmount.toString()}`);
    if (bestPath.fee) {
      console.log(`   手续费: ${bestPath.fee / 10000}%`);
    }
  } else {
    console.log('   ❌ 未找到可用路径');
  }

  // 测试三角套利
  console.log('\n🔺 三角套利测试 (USDT -> BUSD -> WBNB -> USDT):');
  const triangularResult = await dexService.getTriangularArbitragePaths(USDT, BUSD, WBNB, testAmount);

  console.log(
    `   路径1 (USDT->BUSD): ${triangularResult.path1 ? `${triangularResult.path1.version} - ${triangularResult.path1.outputAmount.toString()}` : '❌'}`,
  );
  console.log(
    `   路径2 (BUSD->WBNB): ${triangularResult.path2 ? `${triangularResult.path2.version} - ${triangularResult.path2.outputAmount.toString()}` : '❌'}`,
  );
  console.log(
    `   路径3 (WBNB->USDT): ${triangularResult.path3 ? `${triangularResult.path3.version} - ${triangularResult.path3.outputAmount.toString()}` : '❌'}`,
  );
  console.log(`   总输出: ${triangularResult.totalOutput.toString()}`);
  console.log(`   利润: ${triangularResult.profit.toString()}`);
  console.log(`   是否盈利: ${triangularResult.profitable ? '✅' : '❌'}`);
}
