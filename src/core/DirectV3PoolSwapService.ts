import type { Address } from 'viem';
import { encodeAbiParameters, formatEther, formatUnits, parseAbiParameters, parseEther } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { getTokenInfoByAddress } from '../constants/tokens';
import { publicClient, walletClient } from './BlockchainService';

// 完整的 V3 Pool ABI - 基于用户提供的 BANK/BNB 池子合约 ABI
const v3PoolCompleteAbi = [
  // 基本信息查询
  {
    inputs: [],
    name: 'token0',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'token1',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'fee',
    outputs: [{ internalType: 'uint24', name: '', type: 'uint24' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'liquidity',
    outputs: [{ internalType: 'uint128', name: '', type: 'uint128' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'slot0',
    outputs: [
      { internalType: 'uint160', name: 'sqrtPriceX96', type: 'uint160' },
      { internalType: 'int24', name: 'tick', type: 'int24' },
      { internalType: 'uint16', name: 'observationIndex', type: 'uint16' },
      {
        internalType: 'uint16',
        name: 'observationCardinality',
        type: 'uint16',
      },
      {
        internalType: 'uint16',
        name: 'observationCardinalityNext',
        type: 'uint16',
      },
      { internalType: 'uint32', name: 'feeProtocol', type: 'uint32' },
      { internalType: 'bool', name: 'unlocked', type: 'bool' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  // 核心 swap 函数
  {
    inputs: [
      { internalType: 'address', name: 'recipient', type: 'address' },
      { internalType: 'bool', name: 'zeroForOne', type: 'bool' },
      { internalType: 'int256', name: 'amountSpecified', type: 'int256' },
      { internalType: 'uint160', name: 'sqrtPriceLimitX96', type: 'uint160' },
      { internalType: 'bytes', name: 'data', type: 'bytes' },
    ],
    name: 'swap',
    outputs: [
      { internalType: 'int256', name: 'amount0', type: 'int256' },
      { internalType: 'int256', name: 'amount1', type: 'int256' },
    ],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  // Flash loan 函数（用于实现回调）
  {
    inputs: [
      { internalType: 'address', name: 'recipient', type: 'address' },
      { internalType: 'uint256', name: 'amount0', type: 'uint256' },
      { internalType: 'uint256', name: 'amount1', type: 'uint256' },
      { internalType: 'bytes', name: 'data', type: 'bytes' },
    ],
    name: 'flash',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
] as const;

// ERC20 ABI for token operations
const erc20Abi = [
  {
    inputs: [{ internalType: 'address', name: 'account', type: 'address' }],
    name: 'balanceOf',
    outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: 'spender', type: 'address' },
      { internalType: 'uint256', name: 'amount', type: 'uint256' },
    ],
    name: 'approve',
    outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: 'owner', type: 'address' },
      { internalType: 'address', name: 'spender', type: 'address' },
    ],
    name: 'allowance',
    outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: 'to', type: 'address' },
      { internalType: 'uint256', name: 'amount', type: 'uint256' },
    ],
    name: 'transfer',
    outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'decimals',
    outputs: [{ internalType: 'uint8', name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

/**
 * 直接 Swap 参数接口
 */
export interface DirectSwapParams {
  poolAddress: Address;
  recipient: Address;
  zeroForOne: boolean; // true: token0 -> token1, false: token1 -> token0
  amountSpecified: bigint; // 正数表示精确输入，负数表示精确输出
  sqrtPriceLimitX96: bigint; // 价格限制，0 表示无限制
  slippageTolerance: number; // 滑点容忍度（百分比）
}

/**
 * Swap 结果接口
 */
export interface SwapResult {
  success: boolean;
  transactionHash?: string;
  amount0?: bigint;
  amount1?: bigint;
  gasUsed?: bigint;
  error?: string;
}

/**
 * 池子状态信息
 */
export interface PoolState {
  token0: Address;
  token1: Address;
  fee: number;
  liquidity: bigint;
  sqrtPriceX96: bigint;
  tick: number;
  unlocked: boolean;
}

/**
 * 价格计算结果
 */
export interface PriceCalculation {
  price: number; // token1/token0 的价格
  priceFormatted: string;
  sqrtPriceX96: bigint;
  tick: number;
  token0Symbol: string;
  token1Symbol: string;
}

/**
 * 代币详细信息接口
 */
export interface TokenDetails {
  address: Address;
  symbol: string;
  decimals: number;
  name?: string;
}

/**
 * 修正价格计算结果接口
 */
export interface PriceCalculationWithDecimals {
  price: number;
  priceFormatted: string;
  sqrtPriceX96: bigint;
  tick: number;
  token0Details: TokenDetails;
  token1Details: TokenDetails;
  rawPrice: bigint;
}

/**
 * 直接 V3 池子 Swap 服务
 * 绕过 Quoter，直接与池子合约交互
 */
export class DirectV3PoolSwapService {
  private readonly bankPoolAddress: Address = BSC_CONTRACTS.BNB_BANK_POOL as Address;

  /**
   * 计算基于 sqrtPriceX96 的价格
   */
  async calculatePrice(poolAddress: Address): Promise<PriceCalculation | null> {
    try {
      const [slot0, tokens] = await Promise.all([
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'slot0',
        }),
        this.getPoolTokens(poolAddress),
      ]);

      if (!tokens) {
        return null;
      }

      const sqrtPriceX96 = slot0[0];
      const tick = slot0[1];

      // 计算价格: price = (sqrtPriceX96 / 2^96)^2
      // 这给出了 token1/token0 的价格
      const Q96 = 2n ** 96n;
      const priceRaw = (sqrtPriceX96 * sqrtPriceX96) / Q96;

      // 转换为 number 类型用于显示
      const price = Number(formatUnits(priceRaw, 18));
      const priceFormatted = price.toFixed(6);

      // 获取代币符号
      const token0Info = getTokenInfoByAddress(tokens.token0);
      const token1Info = getTokenInfoByAddress(tokens.token1);

      return {
        price,
        priceFormatted,
        sqrtPriceX96,
        tick,
        token0Symbol: token0Info?.symbol || tokens.token0.slice(0, 6),
        token1Symbol: token1Info?.symbol || tokens.token1.slice(0, 6),
      };
    } catch (error) {
      console.error('计算价格失败:', error);
      return null;
    }
  }

  /**
   * 获取池子代币信息
   */
  async getPoolTokens(poolAddress: Address): Promise<{ token0: Address; token1: Address } | null> {
    try {
      const [token0, token1] = await Promise.all([
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'token0',
        }),
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'token1',
        }),
      ]);

      return { token0, token1 };
    } catch (error) {
      console.error('获取池子代币失败:', error);
      return null;
    }
  }

  /**
   * 检查代币余额和授权
   */
  async checkTokenBalance(
    tokenAddress: Address,
    userAddress: Address,
  ): Promise<{
    balance: bigint;
    balanceFormatted: string;
  }> {
    try {
      const balance = await publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'balanceOf',
        args: [userAddress],
      });

      const decimals = await publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'decimals',
      });

      const balanceFormatted = formatUnits(balance, decimals);

      return { balance, balanceFormatted };
    } catch (error) {
      console.error('检查代币余额失败:', error);
      return { balance: 0n, balanceFormatted: '0' };
    }
  }

  /**
   * 检查代币授权额度
   */
  async checkAllowance(tokenAddress: Address, ownerAddress: Address, spenderAddress: Address): Promise<bigint> {
    try {
      const allowance = await publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'allowance',
        args: [ownerAddress, spenderAddress],
      });

      return allowance;
    } catch (error) {
      console.error('检查授权额度失败:', error);
      return 0n;
    }
  }

  /**
   * 授权代币给池子合约
   */
  async approveToken(tokenAddress: Address, spenderAddress: Address, amount: bigint): Promise<boolean> {
    try {
      if (!walletClient?.account) {
        console.error('钱包客户端未初始化或账户未连接');
        return false;
      }

      const hash = await walletClient.writeContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'approve',
        args: [spenderAddress, amount],
        account: walletClient.account,
        chain: null,
      });

      console.log(`授权交易已提交: ${hash}`);

      // 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({ hash });
      console.log(`授权交易已确认: ${receipt.status === 'success' ? '成功' : '失败'}`);

      return receipt.status === 'success';
    } catch (error) {
      console.error('授权代币失败:', error);
      return false;
    }
  }

  /**
   * 获取完整的池子状态信息
   */
  async getPoolState(poolAddress: Address): Promise<PoolState | null> {
    try {
      const [token0, token1, fee, liquidity, slot0] = await Promise.all([
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'token0',
        }),
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'token1',
        }),
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'fee',
        }),
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'liquidity',
        }),
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'slot0',
        }),
      ]);

      return {
        token0,
        token1,
        fee,
        liquidity,
        sqrtPriceX96: slot0[0],
        tick: slot0[1],
        unlocked: slot0[6],
      };
    } catch (error) {
      console.error('获取池子状态失败:', error);
      return null;
    }
  }

  /**
   * 模拟 swap 以估算输出金额（修正版本 - 使用正确的价格计算）
   */
  async simulateSwap(
    poolAddress: Address,
    zeroForOne: boolean,
    amountSpecified: bigint,
  ): Promise<{
    estimatedAmount0: bigint;
    estimatedAmount1: bigint;
    priceImpact: number;
  } | null> {
    try {
      const poolState = await this.getPoolState(poolAddress);
      if (!poolState) {
        return null;
      }

      const sqrtPriceX96 = poolState.sqrtPriceX96;
      const liquidity = poolState.liquidity;

      // 使用正确的 Uniswap V3 价格计算公式
      // sqrtPriceX96 = sqrt(price) * 2^96
      // price = (sqrtPriceX96 / 2^96)^2
      // 这里的 price 是 token1/token0 的价格

      const Q96 = 2n ** 96n;
      const Q192 = Q96 * Q96;

      // 计算价格：price = (sqrtPriceX96)^2 / 2^192
      // 这给出 token1/token0 的价格（以 token0 为单位的 token1 价格）
      const priceX192 = sqrtPriceX96 * sqrtPriceX96;

      let estimatedAmount0: bigint;
      let estimatedAmount1: bigint;

      if (zeroForOne) {
        // token0 -> token1
        // 输入 amountSpecified 的 token0，输出多少 token1
        estimatedAmount0 = amountSpecified; // 输入为正数

        // 简化计算：outputAmount1 = inputAmount0 * price
        // 但需要考虑精度，price 在 Q192 格式
        estimatedAmount1 = -(amountSpecified * priceX192) / Q192; // 输出为负数
      } else {
        // token1 -> token0
        // 输入 amountSpecified 的 token1，输出多少 token0
        estimatedAmount1 = amountSpecified; // 输入为正数

        // outputAmount0 = inputAmount1 / price
        estimatedAmount0 = -(amountSpecified * Q192) / priceX192; // 输出为负数
      }

      // 简化的价格影响计算
      const liquidityBigInt = BigInt(liquidity);
      const priceImpact = liquidityBigInt > 0n ? (Number(amountSpecified) / Number(liquidityBigInt)) * 100 : 0;

      return {
        estimatedAmount0,
        estimatedAmount1,
        priceImpact: Math.min(Math.abs(priceImpact), 100), // 限制在 100% 以内
      };
    } catch (error) {
      console.error('模拟 swap 失败:', error);
      return null;
    }
  }

  /**
   * 执行直接 swap（真正的池子调用）
   */
  async executeSwap(params: DirectSwapParams): Promise<SwapResult> {
    try {
      console.log('🔄 准备执行直接 V3 池子 swap...');
      console.log(`   池子: ${params.poolAddress}`);
      console.log(`   方向: ${params.zeroForOne ? 'token0 -> token1' : 'token1 -> token0'}`);
      console.log(`   金额: ${formatEther(params.amountSpecified)}`);

      if (!walletClient?.account) {
        return { success: false, error: '钱包未连接' };
      }

      // 1. 获取池子状态
      const poolState = await this.getPoolState(params.poolAddress);
      if (!poolState) {
        return { success: false, error: '无法获取池子状态' };
      }

      // 2. 确定输入代币
      const tokenIn = params.zeroForOne ? poolState.token0 : poolState.token1;

      // 3. 检查余额
      const balance = await this.checkTokenBalance(tokenIn, walletClient.account.address);
      if (balance.balance < params.amountSpecified) {
        return {
          success: false,
          error: `余额不足: 需要 ${formatEther(params.amountSpecified)}, 当前 ${balance.balanceFormatted}`,
        };
      }

      // 4. 检查授权
      const allowance = await this.checkAllowance(tokenIn, walletClient.account.address, params.poolAddress);

      if (allowance < params.amountSpecified) {
        console.log('需要授权代币...');
        const approved = await this.approveToken(
          tokenIn,
          params.poolAddress,
          params.amountSpecified * 2n, // 授权双倍金额以备后用
        );

        if (!approved) {
          return { success: false, error: '代币授权失败' };
        }
      }

      // 5. 模拟交易
      const simulation = await this.simulateSwap(params.poolAddress, params.zeroForOne, params.amountSpecified);

      if (!simulation) {
        return { success: false, error: '无法模拟交易' };
      }

      console.log(`   预估 amount0: ${formatEther(simulation.estimatedAmount0)}`);
      console.log(`   预估 amount1: ${formatEther(simulation.estimatedAmount1)}`);
      console.log(`   价格影响: ${simulation.priceImpact.toFixed(2)}%`);

      // 6. 准备回调数据（简化版本）
      const callbackData = encodeAbiParameters(parseAbiParameters('address payer'), [walletClient.account.address]);

      // 7. 执行真正的 swap 调用
      console.log('🚀 执行真正的池子 swap...');

      const hash = await walletClient.writeContract({
        address: params.poolAddress,
        abi: v3PoolCompleteAbi,
        functionName: 'swap',
        args: [params.recipient, params.zeroForOne, params.amountSpecified, params.sqrtPriceLimitX96, callbackData],
        account: walletClient.account,
        chain: null,
      });

      console.log(`Swap 交易已提交: ${hash}`);

      // 8. 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === 'success') {
        console.log('✅ Swap 交易成功');

        return {
          success: true,
          transactionHash: hash,
          amount0: simulation.estimatedAmount0,
          amount1: simulation.estimatedAmount1,
          gasUsed: receipt.gasUsed,
        };
      }

      return {
        success: false,
        error: '交易失败',
        transactionHash: hash,
      };
    } catch (error) {
      console.error('执行 swap 失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 获取代币的详细信息（包括精度）
   */
  async getTokenDetails(tokenAddress: Address): Promise<TokenDetails | null> {
    try {
      const [decimals, symbol] = await Promise.all([
        publicClient.readContract({
          address: tokenAddress,
          abi: erc20Abi,
          functionName: 'decimals',
        }),
        publicClient.readContract({
          address: tokenAddress,
          abi: [
            {
              inputs: [],
              name: 'symbol',
              outputs: [{ internalType: 'string', name: '', type: 'string' }],
              stateMutability: 'view',
              type: 'function',
            },
          ],
          functionName: 'symbol',
        }),
      ]);

      return {
        address: tokenAddress,
        symbol: symbol as string,
        decimals: decimals as number,
      };
    } catch (error) {
      console.error(`获取代币详情失败 (${tokenAddress}):`, error);
      return null;
    }
  }

  /**
   * 修正的价格计算（考虑代币精度）
   */
  async calculatePriceWithDecimals(poolAddress: Address): Promise<PriceCalculationWithDecimals | null> {
    try {
      const [slot0, tokens] = await Promise.all([
        publicClient.readContract({
          address: poolAddress,
          abi: v3PoolCompleteAbi,
          functionName: 'slot0',
        }),
        this.getPoolTokens(poolAddress),
      ]);

      if (!tokens) {
        return null;
      }

      // 获取代币详细信息
      const [token0Details, token1Details] = await Promise.all([
        this.getTokenDetails(tokens.token0),
        this.getTokenDetails(tokens.token1),
      ]);

      if (!token0Details || !token1Details) {
        return null;
      }

      const sqrtPriceX96 = slot0[0];
      const tick = slot0[1];

      // 计算原始价格: price = (sqrtPriceX96 / 2^96)^2
      const Q96 = 2n ** 96n;
      const rawPrice = (sqrtPriceX96 * sqrtPriceX96) / Q96;

      // 考虑代币精度的价格调整
      const decimalsDiff = token1Details.decimals - token0Details.decimals;
      const adjustmentFactor = 10n ** BigInt(Math.abs(decimalsDiff));

      let adjustedPrice: bigint;
      if (decimalsDiff >= 0) {
        adjustedPrice = rawPrice * adjustmentFactor;
      } else {
        adjustedPrice = rawPrice / adjustmentFactor;
      }

      // 转换为 number 类型用于显示
      const price = Number(formatUnits(adjustedPrice, 18));
      const priceFormatted = price.toFixed(8);

      return {
        price,
        priceFormatted,
        sqrtPriceX96,
        tick,
        token0Details,
        token1Details,
        rawPrice,
      };
    } catch (error) {
      console.error('计算修正价格失败:', error);
      return null;
    }
  }

  /**
   * 批量模拟多个swap操作（并行执行）
   */
  async batchSimulateSwaps(
    swapRequests: Array<{
      poolAddress: Address;
      zeroForOne: boolean;
      amountIn: bigint;
      label?: string;
    }>,
  ): Promise<
    Array<{
      request: (typeof swapRequests)[0];
      result: {
        estimatedAmount0: bigint;
        estimatedAmount1: bigint;
        priceImpact: number;
      } | null;
      error?: string;
    }>
  > {
    const startTime = Date.now();

    // 并行执行所有swap模拟
    const promises = swapRequests.map(async (request) => {
      try {
        const result = await this.simulateSwap(request.poolAddress, request.zeroForOne, request.amountIn);
        return {
          request,
          result,
        };
      } catch (error) {
        return {
          request,
          result: null,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    });

    const results = await Promise.allSettled(promises);
    const duration = Date.now() - startTime;

    console.log(`🚀 批量swap查询完成: ${swapRequests.length}个请求，耗时${duration}ms`);

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      }
      return {
        request: swapRequests[index],
        result: null,
        error: result.reason instanceof Error ? result.reason.message : String(result.reason),
      };
    });
  }
}

/**
 * 测试指定池子地址的直接 V3 池子 swap 服务
 */
export async function testDirectV3PoolSwapWithAddress(poolAddress: Address): Promise<void> {
  console.log(`🧪 测试直接 V3 池子 Swap 服务 (池子: ${poolAddress})...\n`);

  const service = new DirectV3PoolSwapService();

  // 1. 获取池子基本信息
  console.log('📊 获取池子信息:');
  const tokens = await service.getPoolTokens(poolAddress);

  if (tokens) {
    const token0Info = getTokenInfoByAddress(tokens.token0);
    const token1Info = getTokenInfoByAddress(tokens.token1);

    console.log(`   Token0: ${token0Info?.symbol || tokens.token0.slice(0, 8)} (${tokens.token0})`);
    console.log(`   Token1: ${token1Info?.symbol || tokens.token1.slice(0, 8)} (${tokens.token1})`);
  } else {
    console.log('❌ 无法获取池子代币信息');
    return;
  }

  // 2. 获取完整池子状态
  console.log('\n🔍 获取池子状态:');
  const poolState = await service.getPoolState(poolAddress);

  if (poolState) {
    console.log(`   手续费: ${poolState.fee} (${poolState.fee / 10000}%)`);
    console.log(`   流动性: ${poolState.liquidity}`);
    console.log(`   sqrtPriceX96: ${poolState.sqrtPriceX96}`);
    console.log(`   当前 tick: ${poolState.tick}`);
    console.log(`   池子状态: ${poolState.unlocked ? '✅ 解锁' : '❌ 锁定'}`);
  } else {
    console.log('❌ 无法获取池子状态');
    return;
  }

  // 3. 计算当前价格
  console.log('\n💰 计算当前价格:');
  const priceCalc = await service.calculatePrice(poolAddress);

  if (priceCalc) {
    console.log(`   ${priceCalc.token1Symbol}/${priceCalc.token0Symbol} 价格: ${priceCalc.priceFormatted}`);
    console.log(`   ${priceCalc.token0Symbol}/${priceCalc.token1Symbol} 价格: ${(1 / priceCalc.price).toFixed(6)}`);
  } else {
    console.log('❌ 无法计算价格');
  }

  // 4. 模拟不同金额的 swap
  if (tokens && poolState) {
    console.log('\n🔄 模拟 Swap:');
    const token0Info = getTokenInfoByAddress(tokens.token0);
    const token1Info = getTokenInfoByAddress(tokens.token1);

    // 根据代币类型选择合适的测试金额
    let testAmounts: bigint[];
    if (token0Info?.symbol === 'USD1' || token1Info?.symbol === 'USD1') {
      // USD1 相关的池子使用较小金额
      testAmounts = [parseEther('1'), parseEther('10'), parseEther('100')];
    } else if (token0Info?.symbol === 'BANK' || token1Info?.symbol === 'BANK') {
      // BANK 相关的池子使用中等金额
      testAmounts = [parseEther('100'), parseEther('1000'), parseEther('10000')];
    } else {
      // 其他代币使用标准金额
      testAmounts = [parseEther('0.01'), parseEther('0.1'), parseEther('1')];
    }

    for (const amount of testAmounts) {
      console.log(
        `\n--- 模拟 ${formatEther(amount)} ${token0Info?.symbol || 'Token0'} -> ${token1Info?.symbol || 'Token1'} ---`,
      );

      const simulation = await service.simulateSwap(
        poolAddress,
        true, // zeroForOne: token0 -> token1
        amount,
      );

      if (simulation) {
        console.log(`✅ 预估 amount0: ${formatEther(simulation.estimatedAmount0)} ${token0Info?.symbol || 'Token0'}`);
        console.log(`✅ 预估 amount1: ${formatEther(simulation.estimatedAmount1)} ${token1Info?.symbol || 'Token1'}`);
        console.log(`   价格影响: ${simulation.priceImpact.toFixed(4)}%`);
      } else {
        console.log('❌ 模拟失败');
      }
    }

    // 反向模拟
    for (const amount of testAmounts) {
      console.log(
        `\n--- 模拟 ${formatEther(amount)} ${token1Info?.symbol || 'Token1'} -> ${token0Info?.symbol || 'Token0'} ---`,
      );

      const simulation = await service.simulateSwap(
        poolAddress,
        false, // zeroForOne: token1 -> token0
        amount,
      );

      if (simulation) {
        console.log(`✅ 预估 amount0: ${formatEther(simulation.estimatedAmount0)} ${token0Info?.symbol || 'Token0'}`);
        console.log(`✅ 预估 amount1: ${formatEther(simulation.estimatedAmount1)} ${token1Info?.symbol || 'Token1'}`);
        console.log(`   价格影响: ${simulation.priceImpact.toFixed(4)}%`);
      } else {
        console.log('❌ 模拟失败');
      }
    }
  }

  // 5. 测试执行 swap（模拟模式）
  console.log('\n🚀 测试执行 Swap (真实调用):');

  if (!walletClient?.account) {
    console.log('⚠️ 钱包未连接，跳过实际交易测试');
  } else {
    console.log('⚠️ 为安全起见，跳过真实交易测试');
    console.log('   如需测试真实交易，请确保：');
    console.log('   1. 在测试网络上运行');
    console.log('   2. 使用小额测试');
    console.log('   3. 确认池子流动性充足');
  }

  console.log('\n💡 重要提示:');
  console.log('   - 现在使用真正的池子 swap 函数调用');
  console.log('   - 包含了简化的回调处理逻辑');
  console.log('   - 建议先在测试网络上验证');

  console.log('\n✅ 直接 V3 池子 Swap 服务测试完成');
}

/**
 * 测试直接 V3 池子 swap 服务（默认使用 BNB/BANK 池子）
 */
export async function testDirectV3PoolSwap(): Promise<void> {
  const bankPoolAddress = BSC_CONTRACTS.BNB_BANK_POOL as Address;
  await testDirectV3PoolSwapWithAddress(bankPoolAddress);
}

/**
 * 测试 USD1/BANK 池子的直接 swap 功能
 */
export async function testUSD1BankPoolSwap(): Promise<void> {
  console.log('🏦 测试 USD1/BANK 池子直接 Swap 功能...\n');

  const usd1BankPoolAddress = BSC_CONTRACTS.USD1_BANK_POOL as Address;
  console.log(`池子地址: ${usd1BankPoolAddress}`);

  await testDirectV3PoolSwapWithAddress(usd1BankPoolAddress);
}

/**
 * 增强的 USD1/BANK 池子诊断
 */
export async function diagnoseUSD1BankPool(): Promise<void> {
  console.log('🔬 增强的 USD1/BANK 池子诊断...\n');

  const service = new DirectV3PoolSwapService();
  const usd1BankPoolAddress = BSC_CONTRACTS.USD1_BANK_POOL as Address;

  console.log(`池子地址: ${usd1BankPoolAddress}`);

  // 1. 获取池子基本信息
  console.log('\n📊 获取池子基本信息:');
  const tokens = await service.getPoolTokens(usd1BankPoolAddress);

  if (!tokens) {
    console.log('❌ 无法获取池子代币信息');
    return;
  }

  // 2. 获取代币详细信息（包括精度）
  console.log('\n🔍 获取代币详细信息:');
  const [token0Details, token1Details] = await Promise.all([
    service.getTokenDetails(tokens.token0),
    service.getTokenDetails(tokens.token1),
  ]);

  if (!token0Details || !token1Details) {
    console.log('❌ 无法获取代币详细信息');
    return;
  }

  console.log(`   Token0: ${token0Details.symbol}`);
  console.log(`     地址: ${token0Details.address}`);
  console.log(`     精度: ${token0Details.decimals} 位小数`);

  console.log(`   Token1: ${token1Details.symbol}`);
  console.log(`     地址: ${token1Details.address}`);
  console.log(`     精度: ${token1Details.decimals} 位小数`);

  // 3. 获取池子状态
  console.log('\n🏊 获取池子状态:');
  const poolState = await service.getPoolState(usd1BankPoolAddress);

  if (!poolState) {
    console.log('❌ 无法获取池子状态');
    return;
  }

  console.log(`   手续费: ${poolState.fee} (${poolState.fee / 10000}%)`);
  console.log(`   流动性: ${poolState.liquidity}`);
  console.log(`   sqrtPriceX96: ${poolState.sqrtPriceX96}`);
  console.log(`   当前 tick: ${poolState.tick}`);
  console.log(`   池子状态: ${poolState.unlocked ? '✅ 解锁' : '❌ 锁定'}`);

  // 4. 原始价格计算
  console.log('\n💰 原始价格计算:');
  const originalPrice = await service.calculatePrice(usd1BankPoolAddress);

  if (originalPrice) {
    console.log(
      `   原始 ${originalPrice.token1Symbol}/${originalPrice.token0Symbol} 价格: ${originalPrice.priceFormatted}`,
    );
    console.log(
      `   原始 ${originalPrice.token0Symbol}/${originalPrice.token1Symbol} 价格: ${(1 / originalPrice.price).toFixed(8)}`,
    );
  }

  // 5. 修正价格计算（考虑代币精度）
  console.log('\n🔧 修正价格计算（考虑代币精度）:');
  const correctedPrice = await service.calculatePriceWithDecimals(usd1BankPoolAddress);

  if (correctedPrice) {
    console.log(
      `   精度差异: ${correctedPrice.token1Details.decimals} - ${correctedPrice.token0Details.decimals} = ${correctedPrice.token1Details.decimals - correctedPrice.token0Details.decimals}`,
    );
    console.log(`   原始价格 (rawPrice): ${correctedPrice.rawPrice}`);
    console.log(
      `   修正后 ${correctedPrice.token1Details.symbol}/${correctedPrice.token0Details.symbol} 价格: ${correctedPrice.priceFormatted}`,
    );
    console.log(
      `   修正后 ${correctedPrice.token0Details.symbol}/${correctedPrice.token1Details.symbol} 价格: ${(1 / correctedPrice.price).toFixed(8)}`,
    );
  }

  // 6. 手动价格计算验证
  console.log('\n🧮 手动价格计算验证:');
  const Q96 = 2n ** 96n;
  const sqrtPrice = poolState.sqrtPriceX96;
  const price = (sqrtPrice * sqrtPrice) / Q96;

  console.log(`   sqrtPriceX96: ${sqrtPrice}`);
  console.log(`   Q96: ${Q96}`);
  console.log(`   price (raw): ${price}`);
  console.log(`   price (formatted): ${formatUnits(price, 18)}`);

  // 7. 不同精度下的价格计算
  console.log('\n📐 不同精度下的价格计算:');
  for (const decimals of [0, 6, 8, 12, 18]) {
    const priceAtDecimals = formatUnits(price, decimals);
    console.log(`   ${decimals} 位小数: ${priceAtDecimals}`);
  }

  // 8. 实际兑换比例测试
  console.log('\n🔄 实际兑换比例测试:');

  // 使用不同的测试金额
  const testAmounts = [
    { amount: 1n, unit: '1 wei' },
    { amount: 1000000n, unit: '1,000,000 wei' },
    { amount: parseEther('0.000001'), unit: '0.000001 token' },
    { amount: parseEther('0.001'), unit: '0.001 token' },
    { amount: parseEther('1'), unit: '1 token' },
  ];

  for (const test of testAmounts) {
    console.log(`\n--- 测试金额: ${test.unit} ---`);

    // BANK -> USD1
    const bankToUsd1 = await service.simulateSwap(
      usd1BankPoolAddress,
      true, // zeroForOne: BANK -> USD1
      test.amount,
    );

    if (bankToUsd1) {
      console.log(
        `   ${formatUnits(test.amount, token0Details.decimals)} ${token0Details.symbol} -> ${formatUnits(bankToUsd1.estimatedAmount1, token1Details.decimals)} ${token1Details.symbol}`,
      );
    }

    // USD1 -> BANK
    const usd1ToBank = await service.simulateSwap(
      usd1BankPoolAddress,
      false, // zeroForOne: USD1 -> BANK
      test.amount,
    );

    if (usd1ToBank) {
      console.log(
        `   ${formatUnits(test.amount, token1Details.decimals)} ${token1Details.symbol} -> ${formatUnits(usd1ToBank.estimatedAmount0, token0Details.decimals)} ${token0Details.symbol}`,
      );
    }
  }

  console.log('\n✅ USD1/BANK 池子诊断完成');
}

/**
 * 诊断池子的 token 顺序和价格信息
 */
export async function diagnosePoolTokenOrder(): Promise<void> {
  console.log('🔍 诊断三角套利路径中的池子信息...\n');

  const service = new DirectV3PoolSwapService();

  const pools = [
    { name: 'BNB/BANK', address: BSC_CONTRACTS.BNB_BANK_POOL as Address },
    { name: 'BNB/USD1', address: BSC_CONTRACTS.BNB_USD1_POOL as Address },
    { name: 'USD1/BANK', address: BSC_CONTRACTS.USD1_BANK_POOL as Address },
  ];

  for (const pool of pools) {
    console.log(`📊 分析池子: ${pool.name} (${pool.address})`);

    try {
      // 获取池子状态
      const poolState = await service.getPoolState(pool.address);
      if (!poolState) {
        console.log('❌ 无法获取池子状态\n');
        continue;
      }

      // 获取 token 详情
      const token0Details = await service.getTokenDetails(poolState.token0);
      const token1Details = await service.getTokenDetails(poolState.token1);

      console.log(`   Token0: ${token0Details?.symbol || 'Unknown'} (${poolState.token0})`);
      console.log(`   Token1: ${token1Details?.symbol || 'Unknown'} (${poolState.token1})`);
      console.log(`   Fee: ${poolState.fee / 10000}%`);
      console.log(`   Liquidity: ${poolState.liquidity}`);
      console.log(`   SqrtPriceX96: ${poolState.sqrtPriceX96}`);

      // 计算价格
      const priceCalc = await service.calculatePriceWithDecimals(pool.address);
      if (priceCalc) {
        console.log(
          `   价格 (${priceCalc.token1Details.symbol}/${priceCalc.token0Details.symbol}): ${priceCalc.priceFormatted}`,
        );

        // 计算反向价格
        const reversePrice = 1 / priceCalc.price;
        console.log(
          `   反向价格 (${priceCalc.token0Details.symbol}/${priceCalc.token1Details.symbol}): ${reversePrice.toFixed(6)}`,
        );
      }

      console.log('');
    } catch (error) {
      console.log(`❌ 分析失败: ${error instanceof Error ? error.message : '未知错误'}\n`);
    }
  }

  console.log('✅ 池子诊断完成');
}

/**
 * 验证价格计算的准确性
 */
export async function validatePriceCalculations(): Promise<void> {
  console.log('🔍 验证价格计算准确性...\n');

  const service = new DirectV3PoolSwapService();

  // 用户提供的正确汇率
  const correctRates = {
    bnbToBank: 8571, // 1 BNB = 8571 BANK
    bnbToUsd1: 670, // 1 BNB = 670 USD1
    bankToUsd1: 0.077, // 1 BANK = 0.077 USD1
  };

  console.log('📊 用户提供的正确汇率:');
  console.log(`   1 BNB = ${correctRates.bnbToBank} BANK`);
  console.log(`   1 BNB = ${correctRates.bnbToUsd1} USD1`);
  console.log(`   1 BANK = ${correctRates.bankToUsd1} USD1\n`);

  // 测试每个池子
  const pools = [
    {
      name: 'BNB/BANK',
      address: BSC_CONTRACTS.BNB_BANK_POOL as Address,
      expectedRate: correctRates.bnbToBank,
      description: 'BNB -> BANK',
    },
    {
      name: 'BNB/USD1',
      address: BSC_CONTRACTS.BNB_USD1_POOL as Address,
      expectedRate: correctRates.bnbToUsd1,
      description: 'BNB -> USD1',
    },
    {
      name: 'USD1/BANK',
      address: BSC_CONTRACTS.USD1_BANK_POOL as Address,
      expectedRate: correctRates.bankToUsd1,
      description: 'BANK -> USD1',
    },
  ];

  for (const pool of pools) {
    console.log(`📊 验证池子: ${pool.name}`);

    try {
      // 获取池子状态
      const poolState = await service.getPoolState(pool.address);
      if (!poolState) {
        console.log('❌ 无法获取池子状态\n');
        continue;
      }

      // 获取 token 详情
      const token0Details = await service.getTokenDetails(poolState.token0);
      const token1Details = await service.getTokenDetails(poolState.token1);

      console.log(`   Token0: ${token0Details?.symbol} (${poolState.token0})`);
      console.log(`   Token1: ${token1Details?.symbol} (${poolState.token1})`);
      console.log(`   SqrtPriceX96: ${poolState.sqrtPriceX96}`);

      // 使用 1 个单位进行测试
      const testAmount = parseEther('1');

      // 测试两个方向
      console.log(`\n   🧪 测试 ${pool.description}:`);

      if (pool.name === 'BNB/BANK') {
        // BNB -> BANK (需要确定方向)
        const simulation1 = await service.simulateSwap(pool.address, true, testAmount);
        const simulation2 = await service.simulateSwap(pool.address, false, testAmount);

        console.log(
          `   方向1 (zeroForOne=true): 1 ${token0Details?.symbol} -> ${formatEther(simulation1?.estimatedAmount1 || 0n)} ${token1Details?.symbol}`,
        );
        console.log(
          `   方向2 (zeroForOne=false): 1 ${token1Details?.symbol} -> ${formatEther(simulation2?.estimatedAmount0 || 0n)} ${token0Details?.symbol}`,
        );

        // 计算实际汇率
        const rate1 = simulation1 ? Math.abs(Number(formatEther(simulation1.estimatedAmount1))) : 0;
        const rate2 = simulation2 ? Math.abs(Number(formatEther(simulation2.estimatedAmount0))) : 0;

        console.log(`   期望汇率: 1 BNB = ${pool.expectedRate} BANK`);
        console.log(`   计算汇率1: 1 ${token0Details?.symbol} = ${rate1} ${token1Details?.symbol}`);
        console.log(`   计算汇率2: 1 ${token1Details?.symbol} = ${rate2} ${token0Details?.symbol}`);
      } else if (pool.name === 'BNB/USD1') {
        // BNB -> USD1
        const simulation1 = await service.simulateSwap(pool.address, true, testAmount);
        const simulation2 = await service.simulateSwap(pool.address, false, testAmount);

        console.log(
          `   方向1 (zeroForOne=true): 1 ${token0Details?.symbol} -> ${formatEther(simulation1?.estimatedAmount1 || 0n)} ${token1Details?.symbol}`,
        );
        console.log(
          `   方向2 (zeroForOne=false): 1 ${token1Details?.symbol} -> ${formatEther(simulation2?.estimatedAmount0 || 0n)} ${token0Details?.symbol}`,
        );

        const rate1 = simulation1 ? Math.abs(Number(formatEther(simulation1.estimatedAmount1))) : 0;
        const rate2 = simulation2 ? Math.abs(Number(formatEther(simulation2.estimatedAmount0))) : 0;

        console.log(`   期望汇率: 1 BNB = ${pool.expectedRate} USD1`);
        console.log(`   计算汇率1: 1 ${token0Details?.symbol} = ${rate1} ${token1Details?.symbol}`);
        console.log(`   计算汇率2: 1 ${token1Details?.symbol} = ${rate2} ${token0Details?.symbol}`);
      } else if (pool.name === 'USD1/BANK') {
        // BANK -> USD1
        const simulation1 = await service.simulateSwap(pool.address, true, testAmount);
        const simulation2 = await service.simulateSwap(pool.address, false, testAmount);

        console.log(
          `   方向1 (zeroForOne=true): 1 ${token0Details?.symbol} -> ${formatEther(simulation1?.estimatedAmount1 || 0n)} ${token1Details?.symbol}`,
        );
        console.log(
          `   方向2 (zeroForOne=false): 1 ${token1Details?.symbol} -> ${formatEther(simulation2?.estimatedAmount0 || 0n)} ${token0Details?.symbol}`,
        );

        const rate1 = simulation1 ? Math.abs(Number(formatEther(simulation1.estimatedAmount1))) : 0;
        const rate2 = simulation2 ? Math.abs(Number(formatEther(simulation2.estimatedAmount0))) : 0;

        console.log(`   期望汇率: 1 BANK = ${pool.expectedRate} USD1`);
        console.log(`   计算汇率1: 1 ${token0Details?.symbol} = ${rate1} ${token1Details?.symbol}`);
        console.log(`   计算汇率2: 1 ${token1Details?.symbol} = ${rate2} ${token0Details?.symbol}`);
      }

      console.log('');
    } catch (error) {
      console.log(`❌ 验证失败: ${error instanceof Error ? error.message : '未知错误'}\n`);
    }
  }

  console.log('✅ 价格验证完成');
}
