import type { Address } from 'viem';
import { type PoolStateCacheData, smartCacheManager } from './SmartCacheManager';

/**
 * 价格计算结果
 */
export interface PriceCalculationResult {
  outputAmount: bigint;
  priceImpact: number;
  effectivePrice: bigint;
  gasEstimate: bigint;
}

/**
 * 批量价格计算请求
 */
export interface BatchPriceRequest {
  poolAddress: Address;
  zeroForOne: boolean;
  amountIn: bigint;
  requestId: string;
}

/**
 * 批量价格计算结果
 */
export interface BatchPriceResult {
  requestId: string;
  result: PriceCalculationResult | null;
  error?: string;
}

/**
 * 优化的价格计算器
 * 提供高性能的价格计算和缓存优化
 */
export class OptimizedPriceCalculator {
  private readonly Q96 = 2n ** 96n;
  private readonly Q192 = this.Q96 * this.Q96;

  /**
   * 单个价格计算（带缓存）
   */
  async calculatePrice(
    poolAddress: Address,
    poolState: PoolStateCacheData,
    zeroForOne: boolean,
    amountIn: bigint,
  ): Promise<PriceCalculationResult | null> {
    // 生成缓存键
    const cacheKey = smartCacheManager.generatePriceCalculationKey(poolAddress, zeroForOne, amountIn);

    // 尝试从缓存获取
    const cachedResult = smartCacheManager.getPriceCalculation(cacheKey);
    if (cachedResult !== null) {
      return {
        outputAmount: cachedResult,
        priceImpact: 0, // 缓存中不存储价格影响，简化处理
        effectivePrice: this.calculateEffectivePrice(poolState.sqrtPriceX96, zeroForOne),
        gasEstimate: 150000n, // 估算的gas费用
      };
    }

    // 执行价格计算
    const result = this.performPriceCalculation(poolState, zeroForOne, amountIn);

    if (result) {
      // 缓存结果
      smartCacheManager.setPriceCalculation(cacheKey, result.outputAmount);
    }

    return result;
  }

  /**
   * 批量价格计算（并行优化）
   */
  async batchCalculatePrice(
    requests: BatchPriceRequest[],
    poolStates: Map<Address, PoolStateCacheData>,
  ): Promise<BatchPriceResult[]> {
    const startTime = Date.now();
    console.log(`🧮 开始批量价格计算: ${requests.length} 个请求`);

    // 并行处理所有请求
    const promises = requests.map(async (request): Promise<BatchPriceResult> => {
      try {
        const poolState = poolStates.get(request.poolAddress);
        if (!poolState) {
          return {
            requestId: request.requestId,
            result: null,
            error: `池子状态未找到: ${request.poolAddress}`,
          };
        }

        const result = await this.calculatePrice(request.poolAddress, poolState, request.zeroForOne, request.amountIn);

        return {
          requestId: request.requestId,
          result,
        };
      } catch (error) {
        return {
          requestId: request.requestId,
          result: null,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    });

    const results = await Promise.allSettled(promises);
    const successfulResults = results.map((result) =>
      result.status === 'fulfilled'
        ? result.value
        : {
            requestId: 'unknown',
            result: null,
            error: 'Promise rejected',
          },
    );

    const duration = Date.now() - startTime;
    const successCount = successfulResults.filter((r) => r.result !== null).length;

    console.log(`🧮 批量价格计算完成: ${successCount}/${requests.length} 成功, 耗时 ${duration}ms`);

    return successfulResults;
  }

  /**
   * 三角套利路径价格计算（优化版）
   */
  async calculateTriangularArbitragePrice(
    path: {
      step1: { poolAddress: Address; poolState: PoolStateCacheData; zeroForOne: boolean };
      step2: { poolAddress: Address; poolState: PoolStateCacheData; zeroForOne: boolean };
      step3: { poolAddress: Address; poolState: PoolStateCacheData; zeroForOne: boolean };
    },
    inputAmount: bigint,
  ): Promise<{
    step1Output: bigint;
    step2Output: bigint;
    step3Output: bigint;
    totalGasEstimate: bigint;
    priceImpacts: number[];
  } | null> {
    try {
      // 第一步计算
      const step1Result = await this.calculatePrice(
        path.step1.poolAddress,
        path.step1.poolState,
        path.step1.zeroForOne,
        inputAmount,
      );

      if (!step1Result) {
        return null;
      }

      // 第二步计算
      const step2Result = await this.calculatePrice(
        path.step2.poolAddress,
        path.step2.poolState,
        path.step2.zeroForOne,
        step1Result.outputAmount,
      );

      if (!step2Result) {
        return null;
      }

      // 第三步计算
      const step3Result = await this.calculatePrice(
        path.step3.poolAddress,
        path.step3.poolState,
        path.step3.zeroForOne,
        step2Result.outputAmount,
      );

      if (!step3Result) {
        return null;
      }

      return {
        step1Output: step1Result.outputAmount,
        step2Output: step2Result.outputAmount,
        step3Output: step3Result.outputAmount,
        totalGasEstimate: step1Result.gasEstimate + step2Result.gasEstimate + step3Result.gasEstimate,
        priceImpacts: [step1Result.priceImpact, step2Result.priceImpact, step3Result.priceImpact],
      };
    } catch (error) {
      console.error('三角套利价格计算失败:', error);
      return null;
    }
  }

  /**
   * 增量价格计算（用于价格变化监控）
   */
  calculatePriceDelta(
    oldPoolState: PoolStateCacheData,
    newPoolState: PoolStateCacheData,
    zeroForOne: boolean,
    amountIn: bigint,
  ): {
    oldOutput: bigint;
    newOutput: bigint;
    deltaPercent: number;
  } | null {
    try {
      const oldResult = this.performPriceCalculation(oldPoolState, zeroForOne, amountIn);
      const newResult = this.performPriceCalculation(newPoolState, zeroForOne, amountIn);

      if (!oldResult || !newResult) {
        return null;
      }

      const delta =
        newResult.outputAmount > oldResult.outputAmount
          ? newResult.outputAmount - oldResult.outputAmount
          : oldResult.outputAmount - newResult.outputAmount;

      const deltaPercent = oldResult.outputAmount > 0n ? Number((delta * 10000n) / oldResult.outputAmount) / 100 : 0;

      return {
        oldOutput: oldResult.outputAmount,
        newOutput: newResult.outputAmount,
        deltaPercent,
      };
    } catch (error) {
      console.error('增量价格计算失败:', error);
      return null;
    }
  }

  /**
   * 执行实际的价格计算
   */
  private performPriceCalculation(
    poolState: PoolStateCacheData,
    zeroForOne: boolean,
    amountIn: bigint,
  ): PriceCalculationResult | null {
    try {
      if (!poolState.unlocked || poolState.liquidity === 0n) {
        return null;
      }

      const sqrtPriceX96 = poolState.sqrtPriceX96;

      // 简化的价格计算（实际应用中需要更复杂的算法）
      let outputAmount: bigint;

      if (zeroForOne) {
        // token0 -> token1
        // outputAmount = inputAmount * price
        const priceX192 = sqrtPriceX96 * sqrtPriceX96;
        outputAmount = (amountIn * priceX192) / this.Q192;
      } else {
        // token1 -> token0
        // outputAmount = inputAmount / price
        const priceX192 = sqrtPriceX96 * sqrtPriceX96;
        outputAmount = (amountIn * this.Q192) / priceX192;
      }

      // 应用手续费（简化处理）
      const feeRate = BigInt(poolState.fee) * 100n; // 转换为基点
      const feeAmount = (outputAmount * feeRate) / 1000000n;
      outputAmount = outputAmount - feeAmount;

      // 计算价格影响（简化）
      const priceImpact = (Number(amountIn) / Number(poolState.liquidity)) * 100;

      // 计算有效价格
      const effectivePrice = this.calculateEffectivePrice(sqrtPriceX96, zeroForOne);

      return {
        outputAmount,
        priceImpact,
        effectivePrice,
        gasEstimate: 150000n, // 估算的gas费用
      };
    } catch (error) {
      console.error('价格计算执行失败:', error);
      return null;
    }
  }

  /**
   * 计算有效价格
   */
  private calculateEffectivePrice(sqrtPriceX96: bigint, zeroForOne: boolean): bigint {
    const priceX192 = sqrtPriceX96 * sqrtPriceX96;

    if (zeroForOne) {
      return priceX192 / this.Q96; // token0 per token1
    }
    return this.Q192 / priceX192; // token1 per token0
  }

  /**
   * 获取计算器统计信息
   */
  getStats(): {
    cacheStats: ReturnType<typeof smartCacheManager.getStats>;
    calculationCount: number;
  } {
    return {
      cacheStats: smartCacheManager.getStats(),
      calculationCount: 0, // 可以添加计数器来跟踪
    };
  }

  /**
   * 清理计算器缓存
   */
  cleanup(): void {
    smartCacheManager.cleanup();
  }
}

// 全局优化价格计算器实例
export const optimizedPriceCalculator: OptimizedPriceCalculator = new OptimizedPriceCalculator();
