import type { Address } from 'viem';
import { formatEther, formatUnits, parseEther } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { getTokenAddress, getTokenInfo } from '../constants/tokens';
import { DirectQuoterService } from './DirectQuoterService';
import { V3PoolDiagnosticService } from './V3PoolDiagnosticService';

/**
 * BANK 池子兑换比例信息
 */
export interface BankPoolExchangeRate {
  poolAddress: Address;
  token0: Address;
  token1: Address;
  token0Symbol: string;
  token1Symbol: string;
  fee: number;
  feePercentage: string;
  liquidity: bigint;
  sqrtPriceX96: bigint;
  tick: number;
  // 兑换比例信息
  bnbToBankRate: string; // 1 BNB = ? BANK
  bankToBnbRate: string; // 1 BANK = ? BNB
  // 测试交易结果
  testAmounts: {
    bnbIn: string;
    bankOut: string;
    bankIn: string;
    bnbOut: string;
  };
  isActive: boolean;
  lastUpdated: Date;
}

/**
 * BANK 池子专用服务
 * 专门处理 BNB/BANK 交易对 (******************************************)
 */
export class BankPoolService {
  private readonly poolAddress: Address = BSC_CONTRACTS.BNB_BANK_POOL as Address;
  private readonly diagnosticService: V3PoolDiagnosticService;
  private readonly quoterService: DirectQuoterService;

  constructor() {
    this.diagnosticService = new V3PoolDiagnosticService();
    this.quoterService = new DirectQuoterService();
  }

  /**
   * 获取 BNB/BANK 池子的完整兑换比例信息
   */
  async getBankPoolExchangeRate(): Promise<BankPoolExchangeRate | null> {
    console.log(`🏦 获取 BANK 池子兑换比例: ${this.poolAddress}`);

    try {
      // 获取池子基本信息
      const [tokens, fee, liquidity, slot0] = await Promise.all([
        this.diagnosticService.getPoolTokens(this.poolAddress),
        this.diagnosticService.getPoolFee(this.poolAddress),
        this.diagnosticService.getPoolLiquidity(this.poolAddress),
        this.diagnosticService.getPoolSlot0(this.poolAddress),
      ]);

      if (!tokens || fee === null || liquidity === null || !slot0) {
        console.log('❌ 无法获取池子基本信息');
        return null;
      }

      // 确定代币信息
      const bnbAddress = getTokenAddress('WBNB');
      const bankAddress = getTokenAddress('BANK');

      let token0Symbol: string;
      let token1Symbol: string;
      let isBnbToken0: boolean;

      if (tokens.token0.toLowerCase() === bnbAddress.toLowerCase()) {
        token0Symbol = 'WBNB';
        token1Symbol = 'BANK';
        isBnbToken0 = true;
      } else if (tokens.token0.toLowerCase() === bankAddress.toLowerCase()) {
        token0Symbol = 'BANK';
        token1Symbol = 'WBNB';
        isBnbToken0 = false;
      } else {
        console.log('❌ 池子代币不匹配 BNB/BANK 对');
        return null;
      }

      console.log('📊 池子信息:');
      console.log(`   Token0: ${token0Symbol} (${tokens.token0})`);
      console.log(`   Token1: ${token1Symbol} (${tokens.token1})`);
      console.log(`   手续费: ${fee} (${fee / 10000}%)`);
      console.log(`   流动性: ${liquidity}`);
      console.log(`   当前价格 (sqrtPriceX96): ${slot0.sqrtPriceX96}`);

      // 测试不同金额的兑换
      const testAmountBnb = parseEther('1'); // 1 BNB
      const testAmountBank = parseEther('1000'); // 1000 BANK

      // BNB -> BANK
      const bnbToBankQuote = await this.quoterService.quoteSpecificPool({
        tokenIn: bnbAddress,
        tokenOut: bankAddress,
        amountIn: testAmountBnb,
        fee,
        sqrtPriceLimitX96: 0n,
      });

      // BANK -> BNB
      const bankToBnbQuote = await this.quoterService.quoteSpecificPool({
        tokenIn: bankAddress,
        tokenOut: bnbAddress,
        amountIn: testAmountBank,
        fee,
        sqrtPriceLimitX96: 0n,
      });

      let bnbToBankRate = '无法获取';
      let bankToBnbRate = '无法获取';
      const testAmounts = {
        bnbIn: '1.0',
        bankOut: '无法获取',
        bankIn: '1000.0',
        bnbOut: '无法获取',
      };

      if (bnbToBankQuote) {
        const bankOut = formatUnits(bnbToBankQuote.amountOut, 18);
        bnbToBankRate = `1 BNB = ${bankOut} BANK`;
        testAmounts.bankOut = bankOut;
        console.log(`💱 BNB -> BANK: ${bnbToBankRate}`);
      }

      if (bankToBnbQuote) {
        const bnbOut = formatEther(bankToBnbQuote.amountOut);
        const bankToBnbSingleRate = Number(bnbOut) / 1000; // 1000 BANK -> ? BNB, so 1 BANK = ? BNB
        bankToBnbRate = `1 BANK = ${bankToBnbSingleRate.toFixed(8)} BNB`;
        testAmounts.bnbOut = bnbOut;
        console.log(`💱 BANK -> BNB: ${bankToBnbRate}`);
      }

      const isActive = liquidity > 0n && (bnbToBankQuote !== null || bankToBnbQuote !== null);

      return {
        poolAddress: this.poolAddress,
        token0: tokens.token0,
        token1: tokens.token1,
        token0Symbol,
        token1Symbol,
        fee,
        feePercentage: `${fee / 10000}%`,
        liquidity,
        sqrtPriceX96: slot0.sqrtPriceX96,
        tick: slot0.tick,
        bnbToBankRate,
        bankToBnbRate,
        testAmounts,
        isActive,
        lastUpdated: new Date(),
      };
    } catch (error) {
      console.error('❌ 获取 BANK 池子信息失败:', error);
      return null;
    }
  }

  /**
   * 获取指定金额的精确兑换报价
   */
  async getExactQuote(tokenIn: 'BNB' | 'BANK', amountIn: string): Promise<{ amountOut: string; rate: string } | null> {
    const bnbAddress = getTokenAddress('WBNB');
    const bankAddress = getTokenAddress('BANK');

    const fee = await this.diagnosticService.getPoolFee(this.poolAddress);
    if (fee === null) {
      console.log('❌ 无法获取池子手续费');
      return null;
    }

    try {
      let tokenInAddress: Address;
      let tokenOutAddress: Address;
      let amountInBigInt: bigint;
      let decimalsIn: number;
      let decimalsOut: number;

      if (tokenIn === 'BNB') {
        tokenInAddress = bnbAddress;
        tokenOutAddress = bankAddress;
        amountInBigInt = parseEther(amountIn);
        decimalsIn = 18;
        decimalsOut = 18;
      } else {
        tokenInAddress = bankAddress;
        tokenOutAddress = bnbAddress;
        amountInBigInt = parseEther(amountIn); // BANK 也是 18 位小数
        decimalsIn = 18;
        decimalsOut = 18;
      }

      const quote = await this.quoterService.quoteSpecificPool({
        tokenIn: tokenInAddress,
        tokenOut: tokenOutAddress,
        amountIn: amountInBigInt,
        fee,
        sqrtPriceLimitX96: 0n,
      });

      if (!quote) {
        return null;
      }

      const amountOut = formatUnits(quote.amountOut, decimalsOut);
      const rate = `${amountIn} ${tokenIn} = ${amountOut} ${tokenIn === 'BNB' ? 'BANK' : 'BNB'}`;

      return { amountOut, rate };
    } catch (error) {
      console.error(`❌ 获取 ${tokenIn} 兑换报价失败:`, error);
      return null;
    }
  }

  /**
   * 检查池子健康状态
   */
  async checkPoolHealth(): Promise<{
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      const [liquidity, slot0] = await Promise.all([
        this.diagnosticService.getPoolLiquidity(this.poolAddress),
        this.diagnosticService.getPoolSlot0(this.poolAddress),
      ]);

      // 检查流动性
      if (liquidity === null) {
        issues.push('无法读取池子流动性');
      } else if (liquidity === 0n) {
        issues.push('池子流动性为零');
        recommendations.push('等待流动性提供者添加流动性');
      } else if (liquidity < parseEther('1')) {
        issues.push('池子流动性极低');
        recommendations.push('使用较小金额进行交易');
      }

      // 检查价格数据
      if (!slot0) {
        issues.push('无法读取池子价格数据');
      } else if (slot0.sqrtPriceX96 === 0n) {
        issues.push('池子价格数据异常');
      }

      // 测试基本交易
      const testQuote = await this.getExactQuote('BNB', '0.001');
      if (!testQuote) {
        issues.push('无法获取测试报价');
        recommendations.push('检查网络连接和合约状态');
      }

      const isHealthy = issues.length === 0;

      if (isHealthy) {
        recommendations.push('池子状态良好，可以进行交易');
      }

      return { isHealthy, issues, recommendations };
    } catch (error) {
      issues.push(`池子健康检查失败: ${error}`);
      recommendations.push('检查网络连接和 RPC 配置');
      return { isHealthy: false, issues, recommendations };
    }
  }
}

/**
 * 测试 BANK 池子服务
 */
export async function testBankPoolService(): Promise<void> {
  console.log('🏦 测试 BANK 池子服务...\n');

  const service = new BankPoolService();

  // 1. 获取基本兑换比例
  console.log('📊 获取基本兑换比例:');
  const exchangeRate = await service.getBankPoolExchangeRate();

  if (exchangeRate) {
    console.log('\n✅ 池子信息获取成功:');
    console.log(`   池子地址: ${exchangeRate.poolAddress}`);
    console.log(`   代币对: ${exchangeRate.token0Symbol}/${exchangeRate.token1Symbol}`);
    console.log(`   手续费: ${exchangeRate.feePercentage}`);
    console.log(`   流动性: ${exchangeRate.liquidity}`);
    console.log(`   状态: ${exchangeRate.isActive ? '✅ 活跃' : '❌ 非活跃'}`);
    console.log(`   更新时间: ${exchangeRate.lastUpdated.toLocaleString()}`);
    console.log('\n💱 兑换比例:');
    console.log(`   ${exchangeRate.bnbToBankRate}`);
    console.log(`   ${exchangeRate.bankToBnbRate}`);
    console.log('\n🧪 测试交易:');
    console.log(`   ${exchangeRate.testAmounts.bnbIn} BNB -> ${exchangeRate.testAmounts.bankOut} BANK`);
    console.log(`   ${exchangeRate.testAmounts.bankIn} BANK -> ${exchangeRate.testAmounts.bnbOut} BNB`);
  } else {
    console.log('❌ 无法获取池子信息');
  }

  // 2. 测试精确报价
  console.log('\n💰 测试精确报价:');
  const testAmounts = ['0.01', '0.1', '1.0'];

  for (const amount of testAmounts) {
    console.log(`\n--- ${amount} BNB -> BANK ---`);
    const bnbQuote = await service.getExactQuote('BNB', amount);
    if (bnbQuote) {
      console.log(`✅ ${bnbQuote.rate}`);
    } else {
      console.log('❌ 无法获取报价');
    }
  }

  const bankTestAmounts = ['100', '1000', '10000'];
  for (const amount of bankTestAmounts) {
    console.log(`\n--- ${amount} BANK -> BNB ---`);
    const bankQuote = await service.getExactQuote('BANK', amount);
    if (bankQuote) {
      console.log(`✅ ${bankQuote.rate}`);
    } else {
      console.log('❌ 无法获取报价');
    }
  }

  // 3. 检查池子健康状态
  console.log('\n🏥 检查池子健康状态:');
  const health = await service.checkPoolHealth();

  console.log(`状态: ${health.isHealthy ? '✅ 健康' : '❌ 有问题'}`);

  if (health.issues.length > 0) {
    console.log('\n⚠️ 发现的问题:');
    for (const issue of health.issues) {
      console.log(`   - ${issue}`);
    }
  }

  if (health.recommendations.length > 0) {
    console.log('\n💡 建议:');
    for (const rec of health.recommendations) {
      console.log(`   - ${rec}`);
    }
  }

  console.log('\n✅ BANK 池子服务测试完成');
}
