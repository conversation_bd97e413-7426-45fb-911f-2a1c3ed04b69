import type { Address } from 'viem';
import { createPublicClient, webSocket } from 'viem';
import { bsc } from 'viem/chains';
import { BSC_WS_RPC_URL } from '../utils/config';

/**
 * WebSocket连接状态
 */
export enum WebSocketStatus {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR',
}

/**
 * WebSocket服务配置
 */
export interface WebSocketConfig {
  url: string;
  reconnectInterval: number; // 重连间隔（毫秒）
  maxReconnectAttempts: number; // 最大重连次数
  pingInterval: number; // 心跳间隔（毫秒）
}

/**
 * WebSocket RPC请求
 */
export interface WSRPCRequest {
  id: string;
  method: string;
  params: unknown[];
}

/**
 * WebSocket RPC响应
 */
export interface WSRPCResponse {
  id: string;
  result?: unknown;
  error?: {
    code: number;
    message: string;
  };
}

/**
 * WebSocket服务类
 * 提供持久连接、自动重连和批量RPC调用功能
 */
export class WebSocketService {
  private config: WebSocketConfig;
  private status: WebSocketStatus = WebSocketStatus.DISCONNECTED;
  private reconnectAttempts = 0;
  private reconnectTimer?: NodeJS.Timeout;
  private pingTimer?: NodeJS.Timeout;
  private pendingRequests = new Map<
    string,
    {
      resolve: (value: unknown) => void;
      reject: (error: unknown) => void;
      timestamp: number;
    }
  >();
  private requestIdCounter = 0;

  // viem WebSocket客户端
  private wsClient: ReturnType<typeof createPublicClient> | null = null;

  constructor(config?: Partial<WebSocketConfig>) {
    this.config = {
      url: BSC_WS_RPC_URL || '',
      reconnectInterval: 5000, // 5秒
      maxReconnectAttempts: 10,
      pingInterval: 30000, // 30秒
      ...config,
    };

    if (!this.config.url) {
      throw new Error('BSC_WS_RPC_URL is not configured in .env file');
    }
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.status === WebSocketStatus.CONNECTED || this.status === WebSocketStatus.CONNECTING) {
      return;
    }

    this.status = WebSocketStatus.CONNECTING;
    console.log(`🔌 连接WebSocket: ${this.config.url}`);

    try {
      // 创建viem WebSocket客户端
      this.wsClient = createPublicClient({
        chain: bsc,
        transport: webSocket(this.config.url, {
          reconnect: {
            attempts: this.config.maxReconnectAttempts,
            delay: this.config.reconnectInterval,
          },
        }),
      });

      this.status = WebSocketStatus.CONNECTED;
      this.reconnectAttempts = 0;

      console.log('✅ WebSocket连接成功');

      // 启动心跳
      this.startPing();
    } catch (error) {
      console.error('❌ WebSocket连接失败:', error);
      this.status = WebSocketStatus.ERROR;
      this.scheduleReconnect();
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    this.status = WebSocketStatus.DISCONNECTED;

    // 清理定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = undefined;
    }

    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = undefined;
    }

    // 拒绝所有待处理的请求
    for (const [id, request] of this.pendingRequests) {
      request.reject(new Error('WebSocket disconnected'));
    }
    this.pendingRequests.clear();

    this.wsClient = null;
    console.log('🔌 WebSocket已断开');
  }

  /**
   * 获取连接状态
   */
  getStatus(): WebSocketStatus {
    return this.status;
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.status === WebSocketStatus.CONNECTED && this.wsClient !== null;
  }

  /**
   * 获取viem WebSocket客户端
   */
  getClient(): ReturnType<typeof createPublicClient> {
    if (!this.isConnected() || !this.wsClient) {
      throw new Error('WebSocket not connected');
    }
    return this.wsClient;
  }

  /**
   * 批量执行合约读取调用
   */
  async batchReadContracts(
    calls: Array<{
      address: Address;
      abi: readonly unknown[];
      functionName: string;
      args?: readonly unknown[];
    }>,
  ): Promise<unknown[]> {
    if (!this.isConnected() || !this.wsClient) {
      throw new Error('WebSocket not connected');
    }

    const startTime = Date.now();
    console.log(`🚀 WebSocket批量查询 ${calls.length} 个合约调用...`);

    try {
      // 使用viem的批量调用功能
      const results = await Promise.allSettled(
        calls.map((call) => {
          if (!this.wsClient) {
            throw new Error('WebSocket client is null');
          }
          return this.wsClient.readContract({
            address: call.address,
            abi: call.abi,
            functionName: call.functionName,
            args: call.args || [],
          });
        }),
      );

      const duration = Date.now() - startTime;
      const successCount = results.filter((r) => r.status === 'fulfilled').length;

      console.log(`✅ WebSocket批量查询完成，耗时 ${duration}ms (成功: ${successCount}/${calls.length})`);

      return results.map((result) => (result.status === 'fulfilled' ? result.value : null));
    } catch (error) {
      console.error('❌ WebSocket批量查询失败:', error);
      throw error;
    }
  }

  /**
   * 启动心跳
   */
  private startPing(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
    }

    this.pingTimer = setInterval(() => {
      if (this.isConnected()) {
        // 发送心跳请求
        this.sendPing().catch((error) => {
          console.warn('⚠️ WebSocket心跳失败:', error);
          this.scheduleReconnect();
        });
      }
    }, this.config.pingInterval);
  }

  /**
   * 发送心跳
   */
  private async sendPing(): Promise<void> {
    if (!this.isConnected() || !this.wsClient) {
      return;
    }

    try {
      // 使用简单的区块号查询作为心跳
      await this.wsClient.getBlockNumber();
    } catch (error) {
      throw new Error(`Ping failed: ${error}`);
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error(`❌ WebSocket重连失败，已达到最大重连次数 (${this.config.maxReconnectAttempts})`);
      this.status = WebSocketStatus.ERROR;
      return;
    }

    this.status = WebSocketStatus.RECONNECTING;
    this.reconnectAttempts++;

    console.log(`🔄 WebSocket重连中... (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch((error) => {
        console.error('重连失败:', error);
        this.scheduleReconnect();
      });
    }, this.config.reconnectInterval);
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `ws_${++this.requestIdCounter}_${Date.now()}`;
  }
}

// 全局WebSocket服务实例
export const webSocketService: WebSocketService = new WebSocketService();
