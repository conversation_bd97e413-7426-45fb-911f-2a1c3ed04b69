import { type Address, formatEther, parseEther } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { getTokenAddress } from '../constants/tokens';
import type { ArbitrageConfig, ArbitrageOpportunity, ArbitrageStats } from '../types';
import { ARBITRAGE_PATHS, GAS_PRICE_GWEI, MAX_SLIPPAGE_PERCENT, MIN_PROFIT_THRESHOLD_USD } from '../utils/config';
import { MulticallService } from './MulticallService';
import { optimizedPriceCalculator } from './OptimizedPriceCalculator';
import { type PoolStateCacheData, type TokenCacheData, smartCacheManager } from './SmartCacheManager';
import { DexVersion } from './UnifiedDexService';
import { type PoolStateChangeEvent, type PriceChangeThreshold, webSocketEventService } from './WebSocketEventService';
import { webSocketService } from './WebSocketService';

/**
 * 超级优化套利发现器
 * 集成所有优化技术：WebSocket事件订阅、智能缓存、优化价格计算、并行处理
 */
export class SuperOptimizedArbitrageFinder {
  private config: ArbitrageConfig;
  private stats: ArbitrageStats;
  private isMonitoring = false;
  private multicallService: MulticallService;

  // 事件驱动状态
  private poolSubscriptions = new Map<Address, boolean>();
  private lastOpportunityCheck = new Date(0);
  private readonly OPPORTUNITY_CHECK_COOLDOWN = 100; // 100ms冷却时间

  constructor() {
    this.multicallService = new MulticallService();
    this.config = {
      paths: ARBITRAGE_PATHS,
      minProfitThresholdUSD: MIN_PROFIT_THRESHOLD_USD,
      maxSlippagePercent: MAX_SLIPPAGE_PERCENT,
      gasPriceGwei: GAS_PRICE_GWEI,
      checkIntervalMs: 1000, // 1秒间隔（事件驱动时不常用）
    };

    this.stats = {
      totalChecks: 0,
      opportunitiesFound: 0,
      successfulTrades: 0,
      failedTrades: 0,
      totalProfitUSD: 0,
      averageCheckDuration: 0,
      lastCheckTime: new Date(),
    };

    console.log('🚀 SuperOptimizedArbitrageFinder 初始化完成');
    console.log(`   - 监控路径数量: ${this.config.paths.length}`);
    console.log(`   - 最小利润阈值: $${this.config.minProfitThresholdUSD}`);
    console.log('   - 集成优化: WebSocket + 智能缓存 + 优化计算');
  }

  /**
   * 启动事件驱动监控
   */
  async startEventDrivenMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.log('⚠️ 事件驱动监控已在运行中');
      return;
    }

    console.log('🚀 启动事件驱动套利监控...');
    this.isMonitoring = true;

    try {
      // 1. 确保WebSocket连接
      await webSocketService.connect();

      // 2. 预热缓存
      await this.warmupCaches();

      // 3. 订阅池子状态变化事件
      await this.subscribeToPoolEvents();

      console.log('✅ 事件驱动监控启动成功');
    } catch (error) {
      console.error('❌ 事件驱动监控启动失败:', error);
      this.isMonitoring = false;
      throw error;
    }
  }

  /**
   * 停止事件驱动监控
   */
  async stopEventDrivenMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      console.log('⚠️ 事件驱动监控未在运行');
      return;
    }

    this.isMonitoring = false;

    // 清理订阅
    webSocketEventService.cleanup();
    this.poolSubscriptions.clear();

    // 断开WebSocket连接
    await webSocketService.disconnect();

    console.log('🛑 事件驱动监控已停止');
  }

  /**
   * 执行超级优化的单次检查
   */
  async checkOnceSuperOptimized(): Promise<ArbitrageOpportunity[]> {
    const startTime = Date.now();
    console.log('\n🚀 开始超级优化套利检查（集成所有优化技术）...');

    const opportunities: ArbitrageOpportunity[] = [];

    try {
      // 1. 预处理：收集所有需要的池子地址
      const allPoolAddresses = new Set<Address>();
      const allTokenAddresses = new Set<Address>();

      for (const path of this.config.paths) {
        const [tokenA, tokenB, tokenC] = path;

        // 收集代币地址
        allTokenAddresses.add(tokenA);
        allTokenAddresses.add(tokenB);
        allTokenAddresses.add(tokenC);

        // 为正向路径添加池子地址
        this.addPoolAddressesForPath(allPoolAddresses, tokenA, tokenB, tokenC);

        // 为反向路径添加池子地址
        const reversePath = this.generateReversePath(path);
        const [revTokenA, revTokenB, revTokenC] = reversePath;
        this.addPoolAddressesForPath(allPoolAddresses, revTokenA, revTokenB, revTokenC);
      }

      const poolAddressArray = Array.from(allPoolAddresses);
      const tokenAddressArray = Array.from(allTokenAddresses);

      console.log(`📊 预处理完成，需要查询 ${poolAddressArray.length} 个池子, ${tokenAddressArray.length} 个代币`);

      // 2. 智能缓存预热和数据获取
      const dataStartTime = Date.now();
      const { poolStates, tokenInfos } = await this.getOptimizedData(poolAddressArray, tokenAddressArray);
      const dataDuration = Date.now() - dataStartTime;

      console.log(`⚡ 数据获取完成，耗时 ${dataDuration}ms`);
      console.log(`   - 池子状态: ${poolStates.size}/${poolAddressArray.length}`);
      console.log(`   - 代币信息: ${tokenInfos.size}/${tokenAddressArray.length}`);

      // 3. 并行检查所有路径（使用优化的价格计算）
      const pathPromises = this.config.paths.map(async (path, index) => {
        const pathStartTime = Date.now();
        console.log(`📊 开始检查路径 ${index + 1}/${this.config.paths.length}: ${path.join(' -> ')}`);

        try {
          const opportunity = await this.checkTriangularArbitrageSuperOptimized(path, poolStates, tokenInfos);
          const pathDuration = Date.now() - pathStartTime;

          if (opportunity?.profitable) {
            console.log(
              `✅ 路径 ${index + 1} 发现套利机会! 预期利润: $${opportunity.estimatedProfitUSD.toFixed(4)} (耗时: ${pathDuration}ms)`,
            );
            return opportunity;
          }
          console.log(`❌ 路径 ${index + 1} 无套利机会 (耗时: ${pathDuration}ms)`);
          return null;
        } catch (error) {
          const pathDuration = Date.now() - pathStartTime;
          console.log(
            `❌ 路径 ${index + 1} 检查失败: ${error instanceof Error ? error.message : String(error)} (耗时: ${pathDuration}ms)`,
          );
          return null;
        }
      });

      // 4. 等待所有路径检查完成
      const results = await Promise.allSettled(pathPromises);

      // 5. 收集成功的结果
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value) {
          opportunities.push(result.value);
        }
      }

      // 6. 更新统计信息
      this.stats.totalChecks++;
      this.stats.opportunitiesFound += opportunities.length;
      this.stats.lastCheckTime = new Date();

      const duration = Date.now() - startTime;
      this.stats.averageCheckDuration =
        (this.stats.averageCheckDuration * (this.stats.totalChecks - 1) + duration) / this.stats.totalChecks;

      // 7. 显示性能统计
      const cacheStats = smartCacheManager.getStats();
      console.log(`\n🎯 超级优化检查完成 (总耗时: ${duration}ms)`);
      console.log(`   - 数据获取: ${dataDuration}ms (${((dataDuration / duration) * 100).toFixed(1)}%)`);
      console.log(
        `   - 路径分析: ${duration - dataDuration}ms (${(((duration - dataDuration) / duration) * 100).toFixed(1)}%)`,
      );
      console.log(`   - 发现机会: ${opportunities.length}`);
      console.log(`   - 总检查次数: ${this.stats.totalChecks}`);
      console.log(`   - 平均检查耗时: ${this.stats.averageCheckDuration.toFixed(0)}ms`);
      console.log(`   - 缓存命中率: ${(cacheStats.total.hitRate * 100).toFixed(1)}%`);
      console.log(`   - 缓存项目: ${cacheStats.total.totalItems} 个`);

      return opportunities;
    } catch (error) {
      console.error('❌ 超级优化套利检查失败:', error);
      return [];
    }
  }

  /**
   * 预热缓存
   */
  private async warmupCaches(): Promise<void> {
    console.log('🔥 开始缓存预热...');

    // 收集所有需要的地址
    const allPoolAddresses = new Set<Address>();
    const allTokenAddresses = new Set<Address>();

    for (const path of this.config.paths) {
      const [tokenA, tokenB, tokenC] = path;

      allTokenAddresses.add(tokenA);
      allTokenAddresses.add(tokenB);
      allTokenAddresses.add(tokenC);

      this.addPoolAddressesForPath(allPoolAddresses, tokenA, tokenB, tokenC);

      const reversePath = this.generateReversePath(path);
      const [revTokenA, revTokenB, revTokenC] = reversePath;
      this.addPoolAddressesForPath(allPoolAddresses, revTokenA, revTokenB, revTokenC);
    }

    // 预热缓存
    await smartCacheManager.warmupCache(Array.from(allTokenAddresses), Array.from(allPoolAddresses), {
      getTokenInfo: async (address: Address): Promise<TokenCacheData> => {
        // 这里应该调用实际的数据获取方法
        return { symbol: 'UNKNOWN', decimals: 18 };
      },
      getPoolState: async (address: Address): Promise<PoolStateCacheData> => {
        // 这里应该调用实际的数据获取方法
        return {
          token0: '0x' as Address,
          token1: '0x' as Address,
          fee: 2500,
          liquidity: 0n,
          sqrtPriceX96: 0n,
          tick: 0,
          unlocked: true,
        };
      },
    });
  }

  /**
   * 订阅池子状态变化事件
   */
  private async subscribeToPoolEvents(): Promise<void> {
    console.log('📡 订阅池子状态变化事件...');

    // 收集所有需要监控的池子
    const poolsToMonitor = new Set<Address>();

    for (const path of this.config.paths) {
      const [tokenA, tokenB, tokenC] = path;
      this.addPoolAddressesForPath(poolsToMonitor, tokenA, tokenB, tokenC);

      const reversePath = this.generateReversePath(path);
      const [revTokenA, revTokenB, revTokenC] = reversePath;
      this.addPoolAddressesForPath(poolsToMonitor, revTokenA, revTokenB, revTokenC);
    }

    // 为每个池子创建订阅
    for (const poolAddress of poolsToMonitor) {
      const threshold: PriceChangeThreshold = {
        poolAddress,
        minPriceChangePercent: 0.01, // 0.01%的价格变化
        maxUpdateInterval: 5000, // 最多5秒更新一次
      };

      webSocketEventService.subscribeToPoolChanges(threshold, (event: PoolStateChangeEvent) => {
        this.handlePoolStateChange(event);
      });

      this.poolSubscriptions.set(poolAddress, true);
    }

    console.log(`📡 已订阅 ${poolsToMonitor.size} 个池子的状态变化`);
  }

  /**
   * 处理池子状态变化事件
   */
  private async handlePoolStateChange(event: PoolStateChangeEvent): Promise<void> {
    // 更新缓存中的池子状态
    smartCacheManager.invalidatePoolState(event.poolAddress);

    // 检查是否需要触发套利机会检查（避免过于频繁）
    const now = new Date();
    if (now.getTime() - this.lastOpportunityCheck.getTime() < this.OPPORTUNITY_CHECK_COOLDOWN) {
      return;
    }

    this.lastOpportunityCheck = now;

    console.log(`📡 池子状态变化触发套利检查: ${event.poolAddress.slice(0, 8)}...`);

    try {
      // 执行快速套利检查
      const opportunities = await this.checkOnceSuperOptimized();

      if (opportunities.length > 0) {
        console.log(`🎯 事件驱动发现 ${opportunities.length} 个套利机会!`);

        // 这里可以添加实际的交易执行逻辑
        for (const opportunity of opportunities) {
          console.log(
            `   💰 机会: ${opportunity.path.join(' -> ')} 利润: $${opportunity.estimatedProfitUSD.toFixed(4)}`,
          );
        }
      }
    } catch (error) {
      console.error('❌ 事件驱动套利检查失败:', error);
    }
  }

  /**
   * 获取优化的数据（智能缓存 + Multicall）
   */
  private async getOptimizedData(
    poolAddresses: Address[],
    tokenAddresses: Address[],
  ): Promise<{
    poolStates: Map<Address, PoolStateCacheData>;
    tokenInfos: Map<Address, TokenCacheData>;
  }> {
    const poolStates = new Map<Address, PoolStateCacheData>();
    const tokenInfos = new Map<Address, TokenCacheData>();

    // 1. 尝试从缓存获取数据
    const uncachedPools: Address[] = [];
    const uncachedTokens: Address[] = [];

    for (const poolAddress of poolAddresses) {
      const cached = smartCacheManager.getPoolState(poolAddress);
      if (cached) {
        poolStates.set(poolAddress, cached);
      } else {
        uncachedPools.push(poolAddress);
      }
    }

    for (const tokenAddress of tokenAddresses) {
      const cached = smartCacheManager.getTokenInfo(tokenAddress);
      if (cached) {
        tokenInfos.set(tokenAddress, cached);
      } else {
        uncachedTokens.push(tokenAddress);
      }
    }

    console.log(
      `📊 缓存命中: 池子 ${poolStates.size}/${poolAddresses.length}, 代币 ${tokenInfos.size}/${tokenAddresses.length}`,
    );

    // 2. 使用Multicall获取未缓存的数据
    if (uncachedPools.length > 0 || uncachedTokens.length > 0) {
      const { pools, tokens } = await this.multicallService.batchGetCompleteInfo(uncachedPools);

      // 更新缓存并添加到结果
      for (const [poolAddress, poolData] of pools) {
        const cacheData: PoolStateCacheData = {
          token0: poolData.token0,
          token1: poolData.token1,
          fee: poolData.fee,
          liquidity: poolData.liquidity,
          sqrtPriceX96: poolData.sqrtPriceX96,
          tick: poolData.tick,
          unlocked: poolData.unlocked,
        };

        smartCacheManager.setPoolState(poolAddress, cacheData);
        poolStates.set(poolAddress, cacheData);
      }

      for (const [tokenAddress, tokenData] of tokens) {
        const cacheData: TokenCacheData = {
          symbol: tokenData.symbol,
          decimals: tokenData.decimals,
        };

        smartCacheManager.setTokenInfo(tokenAddress, cacheData);
        tokenInfos.set(tokenAddress, cacheData);
      }
    }

    return { poolStates, tokenInfos };
  }

  /**
   * 超级优化的三角套利检查
   */
  private async checkTriangularArbitrageSuperOptimized(
    path: Address[],
    poolStates: Map<Address, PoolStateCacheData>,
    tokenInfos: Map<Address, TokenCacheData>,
  ): Promise<ArbitrageOpportunity | null> {
    if (path.length !== 4) {
      throw new Error('三角套利路径必须包含4个代币 (A->B->C->A)');
    }

    if (path[0] !== path[3]) {
      throw new Error('三角套利路径的首尾代币必须相同');
    }

    const inputAmount = parseEther('1');

    try {
      const reversePath = this.generateReversePath(path);

      // 并行检查正向和反向路径
      const [forwardResult, reverseResult] = await Promise.allSettled([
        this.checkSingleDirectionSuperOptimized(path, inputAmount, '正向', poolStates),
        this.checkSingleDirectionSuperOptimized(reversePath, inputAmount, '反向', poolStates),
      ]);

      const forwardOpportunity = forwardResult.status === 'fulfilled' ? forwardResult.value : null;
      const reverseOpportunity = reverseResult.status === 'fulfilled' ? reverseResult.value : null;

      // 选择更优的方向
      let bestOpportunity: ArbitrageOpportunity | null = null;

      if (forwardOpportunity && reverseOpportunity) {
        bestOpportunity =
          forwardOpportunity.estimatedProfitUSD > reverseOpportunity.estimatedProfitUSD
            ? forwardOpportunity
            : reverseOpportunity;
      } else if (forwardOpportunity) {
        bestOpportunity = forwardOpportunity;
      } else if (reverseOpportunity) {
        bestOpportunity = reverseOpportunity;
      }

      return bestOpportunity;
    } catch (error) {
      console.log(`   ❌ 路径检查失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 超级优化的单方向检查
   */
  private async checkSingleDirectionSuperOptimized(
    path: Address[],
    inputAmount: bigint,
    direction: string,
    poolStates: Map<Address, PoolStateCacheData>,
  ): Promise<ArbitrageOpportunity | null> {
    const [tokenA, tokenB, tokenC] = path;

    try {
      const swapConfigs = this.getSwapConfigurations(tokenA, tokenB, tokenC);
      if (!swapConfigs) {
        return null;
      }

      // 获取池子状态
      const step1PoolState = poolStates.get(swapConfigs.step1.pool);
      const step2PoolState = poolStates.get(swapConfigs.step2.pool);
      const step3PoolState = poolStates.get(swapConfigs.step3.pool);

      if (!step1PoolState || !step2PoolState || !step3PoolState) {
        return null;
      }

      // 使用优化的价格计算器
      const priceResult = await optimizedPriceCalculator.calculateTriangularArbitragePrice(
        {
          step1: {
            poolAddress: swapConfigs.step1.pool,
            poolState: step1PoolState,
            zeroForOne: swapConfigs.step1.zeroForOne,
          },
          step2: {
            poolAddress: swapConfigs.step2.pool,
            poolState: step2PoolState,
            zeroForOne: swapConfigs.step2.zeroForOne,
          },
          step3: {
            poolAddress: swapConfigs.step3.pool,
            poolState: step3PoolState,
            zeroForOne: swapConfigs.step3.zeroForOne,
          },
        },
        inputAmount,
      );

      if (!priceResult) {
        return null;
      }

      // 计算利润
      const profit = priceResult.step3Output > inputAmount ? priceResult.step3Output - inputAmount : BigInt(0);
      const profitPercentage = Number((profit * BigInt(10000)) / inputAmount) / 100;

      // 估算费用
      const estimatedGasCostUSD = Number(formatEther(priceResult.totalGasEstimate)) * 600; // 假设 BNB = $600
      const estimatedProfitUSD = Number(formatEther(profit)) * 600;
      const netProfitUSD = estimatedProfitUSD - estimatedGasCostUSD;
      const profitable = netProfitUSD > this.config.minProfitThresholdUSD;

      if (!profitable) {
        return null;
      }

      return {
        path,
        inputAmount,
        outputAmount: priceResult.step3Output,
        profit,
        profitPercentage,
        estimatedGasCostUSD,
        estimatedProfitUSD: netProfitUSD,
        profitable,
        timestamp: new Date(),
        priceQueries: [
          {
            path: [tokenA, tokenB],
            inputAmount,
            outputAmount: priceResult.step1Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenB, tokenC],
            inputAmount: priceResult.step1Output,
            outputAmount: priceResult.step2Output,
            version: DexVersion.V3,
            fee: 0,
          },
          {
            path: [tokenC, tokenA],
            inputAmount: priceResult.step2Output,
            outputAmount: priceResult.step3Output,
            version: DexVersion.V3,
            fee: 0,
          },
        ],
      };
    } catch (error) {
      console.log(`   ❌ ${direction}路径价格查询失败: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  // 辅助方法（复用现有的实现）
  private generateReversePath(path: Address[]): Address[] {
    if (path.length !== 4) {
      throw new Error('路径必须包含4个代币');
    }
    const [tokenA, tokenB, tokenC] = path;
    return [tokenA, tokenC, tokenB, tokenA];
  }

  private addPoolAddressesForPath(
    poolAddresses: Set<Address>,
    tokenA: Address,
    tokenB: Address,
    tokenC: Address,
  ): void {
    // 实现与ArbitrageFinder相同的逻辑
    if (
      (tokenA === getTokenAddress('WBNB') && tokenB === getTokenAddress('BANK')) ||
      (tokenA === getTokenAddress('BANK') && tokenB === getTokenAddress('WBNB'))
    ) {
      poolAddresses.add(BSC_CONTRACTS.BNB_BANK_POOL as Address);
    }
    // ... 其他池子地址添加逻辑
  }

  private getSwapConfigurations(
    tokenA: Address,
    tokenB: Address,
    tokenC: Address,
  ): {
    step1: { pool: Address; zeroForOne: boolean };
    step2: { pool: Address; zeroForOne: boolean };
    step3: { pool: Address; zeroForOne: boolean };
  } | null {
    // 实现与ArbitrageFinder相同的逻辑
    return null; // 简化实现
  }

  /**
   * 获取统计信息
   */
  getStats(): ArbitrageStats & {
    cacheStats: ReturnType<typeof smartCacheManager.getStats>;
    subscriptionStats: ReturnType<typeof webSocketEventService.getSubscriptionStats>;
  } {
    return {
      ...this.stats,
      cacheStats: smartCacheManager.getStats(),
      subscriptionStats: webSocketEventService.getSubscriptionStats(),
    };
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.stopEventDrivenMonitoring();
    smartCacheManager.destroy();
    optimizedPriceCalculator.cleanup();
    console.log('🗑️ SuperOptimizedArbitrageFinder 已清理');
  }
}

// 全局超级优化套利发现器实例
export const superOptimizedArbitrageFinder: SuperOptimizedArbitrageFinder = new SuperOptimizedArbitrageFinder();
