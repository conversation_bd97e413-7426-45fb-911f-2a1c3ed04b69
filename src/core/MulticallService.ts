import type { Address } from 'viem';
import { decodeFunctionResult, encodeAbiParameters, encodeFunctionData } from 'viem';
import { publicClient } from './BlockchainService';

// Multicall3 合约地址（BSC上的标准地址）
const MULTICALL3_ADDRESS = '0xcA11bde05977b3631167028862bE2a173976CA11' as Address;

// Multicall3 ABI
const multicall3Abi = [
  {
    inputs: [
      {
        components: [
          { internalType: 'address', name: 'target', type: 'address' },
          { internalType: 'bytes', name: 'callData', type: 'bytes' },
        ],
        internalType: 'struct Multicall3.Call[]',
        name: 'calls',
        type: 'tuple[]',
      },
    ],
    name: 'aggregate',
    outputs: [
      { internalType: 'uint256', name: 'blockNumber', type: 'uint256' },
      { internalType: 'bytes[]', name: 'returnData', type: 'bytes[]' },
    ],
    stateMutability: 'payable',
    type: 'function',
  },
] as const;

// V3 Pool ABI 片段
const v3PoolAbi = [
  {
    inputs: [],
    name: 'token0',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'token1',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'fee',
    outputs: [{ internalType: 'uint24', name: '', type: 'uint24' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'liquidity',
    outputs: [{ internalType: 'uint128', name: '', type: 'uint128' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'slot0',
    outputs: [
      { internalType: 'uint160', name: 'sqrtPriceX96', type: 'uint160' },
      { internalType: 'int24', name: 'tick', type: 'int24' },
      { internalType: 'uint16', name: 'observationIndex', type: 'uint16' },
      { internalType: 'uint16', name: 'observationCardinality', type: 'uint16' },
      { internalType: 'uint16', name: 'observationCardinalityNext', type: 'uint16' },
      { internalType: 'uint32', name: 'feeProtocol', type: 'uint32' },
      { internalType: 'bool', name: 'unlocked', type: 'bool' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

// ERC20 ABI 片段
const erc20Abi = [
  {
    inputs: [],
    name: 'decimals',
    outputs: [{ internalType: 'uint8', name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'symbol',
    outputs: [{ internalType: 'string', name: '', type: 'string' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

export interface MulticallPoolData {
  poolAddress: Address;
  token0: Address;
  token1: Address;
  fee: number;
  liquidity: bigint;
  sqrtPriceX96: bigint;
  tick: number;
  unlocked: boolean;
}

export interface MulticallTokenData {
  tokenAddress: Address;
  symbol: string;
  decimals: number;
}

/**
 * Multicall服务 - 使用Multicall3合约进行批量RPC调用
 */
export class MulticallService {
  /**
   * 批量获取池子状态（单次RPC调用）
   */
  async batchGetPoolStates(poolAddresses: Address[]): Promise<Map<Address, MulticallPoolData>> {
    const startTime = Date.now();
    console.log(`🚀 Multicall批量查询 ${poolAddresses.length} 个池子状态...`);

    const results = new Map<Address, MulticallPoolData>();

    if (poolAddresses.length === 0) {
      return results;
    }

    try {
      // 构建所有调用
      const calls: { target: Address; callData: `0x${string}` }[] = [];

      // 为每个池子添加5个调用
      for (const poolAddress of poolAddresses) {
        // token0
        calls.push({
          target: poolAddress,
          callData: encodeFunctionData({
            abi: v3PoolAbi,
            functionName: 'token0',
          }),
        });

        // token1
        calls.push({
          target: poolAddress,
          callData: encodeFunctionData({
            abi: v3PoolAbi,
            functionName: 'token1',
          }),
        });

        // fee
        calls.push({
          target: poolAddress,
          callData: encodeFunctionData({
            abi: v3PoolAbi,
            functionName: 'fee',
          }),
        });

        // liquidity
        calls.push({
          target: poolAddress,
          callData: encodeFunctionData({
            abi: v3PoolAbi,
            functionName: 'liquidity',
          }),
        });

        // slot0
        calls.push({
          target: poolAddress,
          callData: encodeFunctionData({
            abi: v3PoolAbi,
            functionName: 'slot0',
          }),
        });
      }

      console.log(`   构建了 ${calls.length} 个调用 (${poolAddresses.length} 个池子 × 5)`);

      // 执行Multicall
      const result = (await publicClient.readContract({
        address: MULTICALL3_ADDRESS,
        abi: multicall3Abi,
        functionName: 'aggregate',
        args: [calls],
      })) as readonly [bigint, readonly `0x${string}`[]];

      const [blockNumber, returnData] = result;

      console.log(`   Multicall执行成功，区块号: ${blockNumber}`);

      // 解析结果
      for (let i = 0; i < poolAddresses.length; i++) {
        const poolAddress = poolAddresses[i];
        const baseIndex = i * 5;

        try {
          // 解析每个池子的5个返回值
          const token0 = decodeFunctionResult({
            abi: v3PoolAbi,
            functionName: 'token0',
            data: returnData[baseIndex],
          }) as Address;

          const token1 = decodeFunctionResult({
            abi: v3PoolAbi,
            functionName: 'token1',
            data: returnData[baseIndex + 1],
          }) as Address;

          const fee = decodeFunctionResult({
            abi: v3PoolAbi,
            functionName: 'fee',
            data: returnData[baseIndex + 2],
          }) as number;

          const liquidity = decodeFunctionResult({
            abi: v3PoolAbi,
            functionName: 'liquidity',
            data: returnData[baseIndex + 3],
          }) as bigint;

          const slot0 = decodeFunctionResult({
            abi: v3PoolAbi,
            functionName: 'slot0',
            data: returnData[baseIndex + 4],
          }) as readonly [bigint, number, number, number, number, number, boolean];

          results.set(poolAddress, {
            poolAddress,
            token0,
            token1,
            fee,
            liquidity,
            sqrtPriceX96: slot0[0],
            tick: slot0[1],
            unlocked: slot0[6],
          });
        } catch (error) {
          console.error(`解析池子 ${poolAddress} 数据失败:`, error);
        }
      }

      const duration = Date.now() - startTime;
      console.log(`✅ Multicall批量池子查询完成，耗时 ${duration}ms (成功: ${results.size}/${poolAddresses.length})`);

      return results;
    } catch (error) {
      console.error('Multicall批量池子查询失败:', error);
      return results;
    }
  }

  /**
   * 批量获取代币详情（单次RPC调用）
   */
  async batchGetTokenDetails(tokenAddresses: Address[]): Promise<Map<Address, MulticallTokenData>> {
    const startTime = Date.now();
    console.log(`🚀 Multicall批量查询 ${tokenAddresses.length} 个代币详情...`);

    const results = new Map<Address, MulticallTokenData>();

    if (tokenAddresses.length === 0) {
      return results;
    }

    try {
      // 构建所有调用
      const calls: { target: Address; callData: `0x${string}` }[] = [];

      // 为每个代币添加2个调用
      for (const tokenAddress of tokenAddresses) {
        // decimals
        calls.push({
          target: tokenAddress,
          callData: encodeFunctionData({
            abi: erc20Abi,
            functionName: 'decimals',
          }),
        });

        // symbol
        calls.push({
          target: tokenAddress,
          callData: encodeFunctionData({
            abi: erc20Abi,
            functionName: 'symbol',
          }),
        });
      }

      console.log(`   构建了 ${calls.length} 个调用 (${tokenAddresses.length} 个代币 × 2)`);

      // 执行Multicall
      const result = (await publicClient.readContract({
        address: MULTICALL3_ADDRESS,
        abi: multicall3Abi,
        functionName: 'aggregate',
        args: [calls],
      })) as readonly [bigint, readonly `0x${string}`[]];

      const [blockNumber, returnData] = result;

      console.log(`   Multicall执行成功，区块号: ${blockNumber}`);

      // 解析结果
      for (let i = 0; i < tokenAddresses.length; i++) {
        const tokenAddress = tokenAddresses[i];
        const baseIndex = i * 2;

        try {
          // 解析每个代币的2个返回值
          const decimals = decodeFunctionResult({
            abi: erc20Abi,
            functionName: 'decimals',
            data: returnData[baseIndex],
          }) as number;

          const symbol = decodeFunctionResult({
            abi: erc20Abi,
            functionName: 'symbol',
            data: returnData[baseIndex + 1],
          }) as string;

          results.set(tokenAddress, {
            tokenAddress,
            symbol,
            decimals,
          });
        } catch (error) {
          console.error(`解析代币 ${tokenAddress} 数据失败:`, error);
        }
      }

      const duration = Date.now() - startTime;
      console.log(`✅ Multicall批量代币查询完成，耗时 ${duration}ms (成功: ${results.size}/${tokenAddresses.length})`);

      return results;
    } catch (error) {
      console.error('Multicall批量代币查询失败:', error);
      return results;
    }
  }

  /**
   * 批量获取完整的池子和代币信息（最多2次RPC调用）
   */
  async batchGetCompleteInfo(poolAddresses: Address[]): Promise<{
    pools: Map<Address, MulticallPoolData>;
    tokens: Map<Address, MulticallTokenData>;
  }> {
    const startTime = Date.now();
    console.log(`🚀 Multicall批量获取 ${poolAddresses.length} 个池子的完整信息...`);

    // 1. 批量获取池子状态
    const pools = await this.batchGetPoolStates(poolAddresses);

    // 2. 收集所有代币地址
    const tokenAddresses = new Set<Address>();
    for (const poolData of pools.values()) {
      tokenAddresses.add(poolData.token0);
      tokenAddresses.add(poolData.token1);
    }

    // 3. 批量获取代币详情
    const tokens = await this.batchGetTokenDetails(Array.from(tokenAddresses));

    const duration = Date.now() - startTime;
    console.log(`✅ Multicall批量完整信息查询完成，总耗时 ${duration}ms`);
    console.log(`   - 池子: ${pools.size}/${poolAddresses.length}`);
    console.log(`   - 代币: ${tokens.size}/${tokenAddresses.size}`);

    return { pools, tokens };
  }
}
