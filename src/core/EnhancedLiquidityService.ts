import type { Address } from 'viem';
import { formatEther, parseEther } from 'viem';
import { type TokenSymbol, getTokenAddress, getTokenInfo } from '../constants/tokens';
import { type PriceQuote, UnifiedDexService } from './UnifiedDexService';

/**
 * 流动性检测结果
 */
export interface LiquidityAnalysis {
  tokenPair: [Address, Address];
  tokenSymbols: [string, string];
  v2Available: boolean;
  v3Available: boolean;
  v3Fees: number[];
  bestQuote: PriceQuote | null;
  liquidityScore: number; // 0-100
  recommendedAmount: bigint;
  issues: string[];
}

/**
 * 三角套利路径分析
 */
export interface TriangularPathAnalysis {
  path: Address[];
  pathSymbols: string[];
  step1: LiquidityAnalysis;
  step2: LiquidityAnalysis;
  step3: LiquidityAnalysis;
  overallScore: number;
  feasible: boolean;
  recommendations: string[];
  alternativePaths: Address[][];
}

/**
 * 增强的流动性检测服务
 * 专门针对 BANK 和 USD1 代币进行深度流动性分析
 */
export class EnhancedLiquidityService {
  private dexService: UnifiedDexService;

  constructor() {
    this.dexService = new UnifiedDexService();
  }

  /**
   * 分析代币对的流动性
   */
  async analyzePairLiquidity(
    tokenA: Address,
    tokenB: Address,
    testAmounts: bigint[] = [parseEther('0.1'), parseEther('1'), parseEther('10')],
  ): Promise<LiquidityAnalysis> {
    const tokenAInfo = this.getTokenSymbol(tokenA);
    const tokenBInfo = this.getTokenSymbol(tokenB);

    console.log(`🔍 分析流动性: ${tokenAInfo} -> ${tokenBInfo}`);

    // 检查路径可用性
    const pathConfig = await this.dexService.checkPathAvailability([tokenA, tokenB]);

    let bestQuote: PriceQuote | null = null;
    let liquidityScore = 0;
    let recommendedAmount = parseEther('1');
    const issues: string[] = [];

    // 测试不同金额的流动性
    for (const amount of testAmounts) {
      try {
        const quote = await this.dexService.getBestQuote(amount, [tokenA, tokenB]);
        if (quote) {
          bestQuote = quote;
          const outputRatio = Number(formatEther(quote.outputAmount)) / Number(formatEther(amount));

          // 计算流动性评分
          if (outputRatio > 0.95) liquidityScore = Math.max(liquidityScore, 90);
          else if (outputRatio > 0.9) liquidityScore = Math.max(liquidityScore, 70);
          else if (outputRatio > 0.8) liquidityScore = Math.max(liquidityScore, 50);
          else liquidityScore = Math.max(liquidityScore, 20);

          recommendedAmount = amount;
          break;
        }
      } catch (error) {
        issues.push(`金额 ${formatEther(amount)} 测试失败: ${error}`);
      }
    }

    // 分析问题
    if (!pathConfig.v2Available && !pathConfig.v3Available) {
      issues.push('V2 和 V3 都没有可用的流动性池');
      liquidityScore = 0;
    } else if (!bestQuote) {
      issues.push('无法获取有效报价');
      liquidityScore = Math.min(liquidityScore, 10);
    }

    return {
      tokenPair: [tokenA, tokenB],
      tokenSymbols: [tokenAInfo, tokenBInfo],
      v2Available: pathConfig.v2Available,
      v3Available: pathConfig.v3Available,
      v3Fees: pathConfig.v3Fees || [],
      bestQuote,
      liquidityScore,
      recommendedAmount,
      issues,
    };
  }

  /**
   * 分析三角套利路径的完整流动性
   */
  async analyzeTriangularPath(path: Address[]): Promise<TriangularPathAnalysis> {
    if (path.length !== 4 || path[0] !== path[3]) {
      throw new Error('无效的三角套利路径');
    }

    const [tokenA, tokenB, tokenC] = path;
    const pathSymbols = [tokenA, tokenB, tokenC, tokenA].map((addr) => this.getTokenSymbol(addr));

    console.log(`\n🔺 分析三角套利路径: ${pathSymbols.join(' -> ')}`);

    // 分析每一步的流动性
    const step1 = await this.analyzePairLiquidity(tokenA, tokenB);
    const step2 = await this.analyzePairLiquidity(tokenB, tokenC);
    const step3 = await this.analyzePairLiquidity(tokenC, tokenA);

    // 计算整体评分
    const overallScore = Math.min(step1.liquidityScore, step2.liquidityScore, step3.liquidityScore);
    const feasible = overallScore > 30; // 至少需要30分才认为可行

    // 生成建议
    const recommendations: string[] = [];
    const alternativePaths: Address[][] = [];

    if (!feasible) {
      recommendations.push('当前路径流动性不足，建议考虑以下优化：');

      if (step1.liquidityScore < 30) {
        recommendations.push(`- ${step1.tokenSymbols.join(' -> ')} 流动性不足，考虑通过中间代币路由`);
        alternativePaths.push(...(await this.generateAlternativePaths(tokenA, tokenB)));
      }

      if (step2.liquidityScore < 30) {
        recommendations.push(`- ${step2.tokenSymbols.join(' -> ')} 流动性不足，考虑通过中间代币路由`);
        alternativePaths.push(...(await this.generateAlternativePaths(tokenB, tokenC)));
      }

      if (step3.liquidityScore < 30) {
        recommendations.push(`- ${step3.tokenSymbols.join(' -> ')} 流动性不足，考虑通过中间代币路由`);
        alternativePaths.push(...(await this.generateAlternativePaths(tokenC, tokenA)));
      }
    } else {
      recommendations.push('路径可行，建议优化：');
      recommendations.push(`- 推荐交易金额: ${formatEther(step1.recommendedAmount)} ${pathSymbols[0]}`);

      if (step1.bestQuote) recommendations.push(`- 步骤1最佳版本: ${step1.bestQuote.version}`);
      if (step2.bestQuote) recommendations.push(`- 步骤2最佳版本: ${step2.bestQuote.version}`);
      if (step3.bestQuote) recommendations.push(`- 步骤3最佳版本: ${step3.bestQuote.version}`);
    }

    return {
      path,
      pathSymbols,
      step1,
      step2,
      step3,
      overallScore,
      feasible,
      recommendations,
      alternativePaths: alternativePaths.slice(0, 3), // 限制为前3个备选路径
    };
  }

  /**
   * 生成备选路径
   */
  private async generateAlternativePaths(tokenA: Address, tokenB: Address): Promise<Address[][]> {
    const intermediateTokens = [
      getTokenAddress('WBNB'),
      getTokenAddress('USDT'),
      getTokenAddress('BUSD'),
      getTokenAddress('USDC'),
    ].filter((addr) => addr !== tokenA && addr !== tokenB);

    const alternatives: Address[][] = [];

    for (const intermediate of intermediateTokens) {
      // 检查 A -> intermediate -> B 路径
      try {
        const path1Available = await this.dexService.checkPathAvailability([tokenA, intermediate]);
        const path2Available = await this.dexService.checkPathAvailability([intermediate, tokenB]);

        if (
          (path1Available.v2Available || path1Available.v3Available) &&
          (path2Available.v2Available || path2Available.v3Available)
        ) {
          alternatives.push([tokenA, intermediate, tokenB]);
        }
      } catch (error) {
        // 忽略错误，继续检查下一个
      }
    }

    return alternatives;
  }

  /**
   * 获取代币符号（用于显示）
   */
  public getTokenSymbol(address: Address): string {
    try {
      const tokenInfo = Object.values(getTokenInfo).find(
        (info) => typeof info === 'object' && info.address?.toLowerCase() === address.toLowerCase(),
      );
      return tokenInfo?.symbol || `${address.slice(0, 6)}...${address.slice(-4)}`;
    } catch {
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    }
  }
}

/**
 * 测试 BANK 代币三角套利路径
 */
export async function testBankArbitragePath(): Promise<void> {
  console.log('🏦 测试 BANK 代币三角套利路径分析...\n');

  const service = new EnhancedLiquidityService();

  // 定义测试路径
  const testPaths = [
    [getTokenAddress('BNB'), getTokenAddress('BANK'), getTokenAddress('USD1'), getTokenAddress('BNB')],
    [getTokenAddress('WBNB'), getTokenAddress('BANK'), getTokenAddress('USDT'), getTokenAddress('WBNB')],
    [getTokenAddress('USDT'), getTokenAddress('BANK'), getTokenAddress('USD1'), getTokenAddress('USDT')],
  ];

  for (let i = 0; i < testPaths.length; i++) {
    const path = testPaths[i] as Address[];
    console.log(`\n📊 测试路径 ${i + 1}:`);

    try {
      const analysis = await service.analyzeTriangularPath(path);

      console.log(`路径: ${analysis.pathSymbols.join(' -> ')}`);
      console.log(`整体评分: ${analysis.overallScore}/100`);
      console.log(`可行性: ${analysis.feasible ? '✅ 可行' : '❌ 不可行'}`);

      console.log('\n各步骤分析:');
      console.log(`  步骤1 (${analysis.step1.tokenSymbols.join(' -> ')}): ${analysis.step1.liquidityScore}/100`);
      console.log(`  步骤2 (${analysis.step2.tokenSymbols.join(' -> ')}): ${analysis.step2.liquidityScore}/100`);
      console.log(`  步骤3 (${analysis.step3.tokenSymbols.join(' -> ')}): ${analysis.step3.liquidityScore}/100`);

      console.log('\n建议:');
      for (const rec of analysis.recommendations) {
        console.log(`  ${rec}`);
      }

      if (analysis.alternativePaths.length > 0) {
        console.log('\n备选路径:');
        analysis.alternativePaths.forEach((altPath, idx) => {
          const altSymbols = altPath.map((addr) => service.getTokenSymbol(addr));
          console.log(`  ${idx + 1}. ${altSymbols.join(' -> ')}`);
        });
      }
    } catch (error) {
      console.log(`❌ 路径分析失败: ${error}`);
    }
  }

  console.log('\n💡 总结建议:');
  console.log('1. BANK 代币流动性较低，建议使用较小的交易金额');
  console.log('2. 考虑使用多跳路径通过主流代币进行交易');
  console.log('3. 监控不同时间段的流动性变化');
  console.log('4. 考虑在其他 DEX 上寻找更好的流动性');
}
