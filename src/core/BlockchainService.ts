import { http, type Account, type PublicClient, type WalletClient, createPublicClient, createWalletClient } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { bsc } from 'viem/chains';
import { BSC_RPC_URL, PRIVATE_KEY } from '../utils/config';

if (!BSC_RPC_URL) {
  throw new Error('BSC_RPC_URL is not defined in the environment variables.');
}

// 创建 publicClient（只读，不需要私钥）
export const publicClient: PublicClient = createPublicClient({
  chain: bsc,
  transport: http(BSC_RPC_URL),
});

// 创建 walletClient（需要私钥，用于交易）
let walletClient: WalletClient | null = null;

export function getWalletClient(): WalletClient {
  if (!walletClient) {
    if (!PRIVATE_KEY) {
      throw new Error('PRIVATE_KEY is required for wallet operations. Please set it in your .env file.');
    }

    const account: Account = privateKeyToAccount(`0x${PRIVATE_KEY}` as `0x${string}`);

    walletClient = createWalletClient({
      account,
      chain: bsc,
      transport: http(BSC_RPC_URL),
    });
  }

  return walletClient;
}

// 导出 walletClient（向后兼容）
export { walletClient };

console.log('BlockchainService initialized with Public Client.');
if (PRIVATE_KEY) {
  console.log('Private key detected - Wallet Client available for transactions.');
} else {
  console.log('No private key - Only read-only operations available.');
}
