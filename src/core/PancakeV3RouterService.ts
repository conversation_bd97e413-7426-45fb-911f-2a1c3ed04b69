import type { Address } from 'viem';
import { formatEther, formatUnits, parseAbiItem, parseEther } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { getTokenAddress, getTokenInfo } from '../constants/tokens';
import { publicClient, walletClient } from './BlockchainService';

// PancakeSwap V3 Router ABI - 核心 swap 函数
const v3RouterAbi = [
  {
    name: 'exactInputSingle',
    type: 'function',
    stateMutability: 'payable',
    inputs: [
      {
        name: 'params',
        type: 'tuple',
        components: [
          { name: 'tokenIn', type: 'address' },
          { name: 'tokenOut', type: 'address' },
          { name: 'fee', type: 'uint24' },
          { name: 'recipient', type: 'address' },
          { name: 'deadline', type: 'uint256' },
          { name: 'amountIn', type: 'uint256' },
          { name: 'amountOutMinimum', type: 'uint256' },
          { name: 'sqrtPriceLimitX96', type: 'uint160' },
        ],
      },
    ],
    outputs: [{ name: 'amountOut', type: 'uint256' }],
  },
] as const;

// ERC20 ABI for approvals
const erc20Abi = [
  parseAbiItem('function approve(address spender, uint256 amount) external returns (bool)'),
  parseAbiItem('function allowance(address owner, address spender) external view returns (uint256)'),
  parseAbiItem('function balanceOf(address account) external view returns (uint256)'),
  parseAbiItem('function decimals() external view returns (uint8)'),
] as const;

/**
 * 单跳 swap 参数
 */
export interface ExactInputSingleParams {
  tokenIn: Address;
  tokenOut: Address;
  fee: number; // 手续费等级 (500, 2500, 10000)
  recipient: Address;
  deadline: bigint;
  amountIn: bigint;
  amountOutMinimum: bigint;
  sqrtPriceLimitX96: bigint;
}

/**
 * Swap 结果
 */
export interface SwapResult {
  success: boolean;
  transactionHash?: string;
  amountOut?: bigint;
  gasUsed?: bigint;
  error?: string;
}

/**
 * PancakeSwap V3 Router 服务
 * 使用官方 Router 合约进行安全的 swap 操作
 */
export class PancakeV3RouterService {
  private readonly routerAddress: Address = BSC_CONTRACTS.PANCAKESWAP_V3_ROUTER as Address;

  /**
   * 检查代币授权
   */
  async checkAllowance(tokenAddress: Address, ownerAddress: Address, spenderAddress: Address): Promise<bigint> {
    try {
      const allowance = await publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'allowance',
        args: [ownerAddress, spenderAddress],
      });
      return allowance;
    } catch (error) {
      console.error('检查授权失败:', error);
      return 0n;
    }
  }

  /**
   * 授权代币给 Router
   */
  async approveToken(tokenAddress: Address, amount: bigint): Promise<boolean> {
    try {
      if (!walletClient?.account) {
        console.error('钱包未连接');
        return false;
      }

      const hash = await walletClient.writeContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'approve',
        args: [this.routerAddress, amount],
        account: walletClient.account,
        chain: null,
      });

      console.log(`授权交易已提交: ${hash}`);

      const receipt = await publicClient.waitForTransactionReceipt({ hash });
      console.log(`授权交易已确认: ${receipt.status === 'success' ? '成功' : '失败'}`);

      return receipt.status === 'success';
    } catch (error) {
      console.error('授权失败:', error);
      return false;
    }
  }

  /**
   * 检查代币余额
   */
  async checkBalance(
    tokenAddress: Address,
    userAddress: Address,
  ): Promise<{
    balance: bigint;
    balanceFormatted: string;
  }> {
    try {
      const [balance, decimals] = await Promise.all([
        publicClient.readContract({
          address: tokenAddress,
          abi: erc20Abi,
          functionName: 'balanceOf',
          args: [userAddress],
        }),
        publicClient.readContract({
          address: tokenAddress,
          abi: erc20Abi,
          functionName: 'decimals',
        }),
      ]);

      const balanceFormatted = formatUnits(balance, decimals);
      return { balance, balanceFormatted };
    } catch (error) {
      console.error('检查余额失败:', error);
      return { balance: 0n, balanceFormatted: '0' };
    }
  }

  /**
   * 执行单跳精确输入 swap
   */
  async exactInputSingle(params: ExactInputSingleParams): Promise<SwapResult> {
    try {
      console.log('🔄 准备执行 V3 Router exactInputSingle...');
      console.log(`   ${params.tokenIn} -> ${params.tokenOut}`);
      console.log(`   金额: ${formatEther(params.amountIn)}`);
      console.log(`   手续费: ${params.fee / 10000}%`);

      if (!walletClient?.account) {
        return { success: false, error: '钱包未连接' };
      }

      // 1. 检查余额
      const balance = await this.checkBalance(params.tokenIn, walletClient.account.address);
      if (balance.balance < params.amountIn) {
        return {
          success: false,
          error: `余额不足: 需要 ${formatEther(params.amountIn)}, 当前 ${balance.balanceFormatted}`,
        };
      }

      // 2. 检查授权
      const allowance = await this.checkAllowance(params.tokenIn, walletClient.account.address, this.routerAddress);

      if (allowance < params.amountIn) {
        console.log('需要授权代币...');
        const approved = await this.approveToken(params.tokenIn, params.amountIn * 2n);
        if (!approved) {
          return { success: false, error: '代币授权失败' };
        }
      }

      // 3. 估算 gas
      const gasEstimate = await publicClient.estimateContractGas({
        address: this.routerAddress,
        abi: v3RouterAbi,
        functionName: 'exactInputSingle',
        args: [params] as never,
        account: walletClient.account,
      });

      console.log(`   预估 Gas: ${gasEstimate}`);

      // 4. 执行 swap
      const hash = await walletClient.writeContract({
        address: this.routerAddress,
        abi: v3RouterAbi,
        functionName: 'exactInputSingle',
        args: [params] as never,
        account: walletClient.account,
        chain: null,
        gas: gasEstimate + gasEstimate / 10n, // 增加 10% gas 缓冲
      });

      console.log(`Swap 交易已提交: ${hash}`);

      // 5. 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === 'success') {
        console.log('✅ Swap 交易成功');

        // 从交易日志中解析实际输出金额（简化版本）
        const amountOut = params.amountOutMinimum; // 实际应该从日志解析

        return {
          success: true,
          transactionHash: hash,
          amountOut,
          gasUsed: receipt.gasUsed,
        };
      }

      return {
        success: false,
        error: '交易失败',
        transactionHash: hash,
      };
    } catch (error) {
      console.error('Swap 执行失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 创建 BANK/BNB swap 参数
   */
  createBankSwapParams(
    direction: 'BNB_TO_BANK' | 'BANK_TO_BNB',
    amountIn: bigint,
    slippageTolerance = 1, // 1%
  ): ExactInputSingleParams {
    const bnbAddress = getTokenAddress('WBNB');
    const bankAddress = getTokenAddress('BANK');
    const fee = 10000; // 1% 手续费池子

    if (!walletClient?.account) {
      throw new Error('钱包未连接');
    }

    const deadline = BigInt(Math.floor(Date.now() / 1000) + 1800); // 30 分钟后过期

    // 简化的最小输出计算（实际应该通过 Quoter 获取）
    const amountOutMinimum = (amountIn * BigInt(100 - slippageTolerance)) / 100n;

    if (direction === 'BNB_TO_BANK') {
      return {
        tokenIn: bnbAddress,
        tokenOut: bankAddress,
        fee,
        recipient: walletClient.account.address,
        deadline,
        amountIn,
        amountOutMinimum,
        sqrtPriceLimitX96: 0n, // 无价格限制
      };
    }

    return {
      tokenIn: bankAddress,
      tokenOut: bnbAddress,
      fee,
      recipient: walletClient.account.address,
      deadline,
      amountIn,
      amountOutMinimum,
      sqrtPriceLimitX96: 0n,
    };
  }
}

/**
 * 测试 PancakeSwap V3 Router 服务
 */
export async function testPancakeV3Router(): Promise<void> {
  console.log('🥞 测试 PancakeSwap V3 Router 服务...\n');

  const service = new PancakeV3RouterService();

  // 1. 检查钱包连接
  if (!walletClient?.account) {
    console.log('⚠️ 钱包未连接，无法执行实际交易');
    console.log('💡 提示: 确保 .env 文件中设置了 PRIVATE_KEY');
    return;
  }

  console.log(`📱 钱包地址: ${walletClient.account.address}`);

  // 2. 检查代币余额
  console.log('\n💰 检查代币余额:');
  const bnbAddress = getTokenAddress('WBNB');
  const bankAddress = getTokenAddress('BANK');

  const [bnbBalance, bankBalance] = await Promise.all([
    service.checkBalance(bnbAddress, walletClient.account.address),
    service.checkBalance(bankAddress, walletClient.account.address),
  ]);

  console.log(`   WBNB 余额: ${bnbBalance.balanceFormatted}`);
  console.log(`   BANK 余额: ${bankBalance.balanceFormatted}`);

  // 3. 检查授权状态
  console.log('\n🔐 检查授权状态:');
  const routerAddress = BSC_CONTRACTS.PANCAKESWAP_V3_ROUTER as Address;

  const [bnbAllowance, bankAllowance] = await Promise.all([
    service.checkAllowance(bnbAddress, walletClient.account.address, routerAddress),
    service.checkAllowance(bankAddress, walletClient.account.address, routerAddress),
  ]);

  console.log(`   WBNB 授权额度: ${formatEther(bnbAllowance)}`);
  console.log(`   BANK 授权额度: ${formatEther(bankAllowance)}`);

  // 4. 模拟创建 swap 参数
  console.log('\n🔧 创建 Swap 参数:');
  try {
    const testAmount = parseEther('0.01'); // 0.01 BNB
    const swapParams = service.createBankSwapParams('BNB_TO_BANK', testAmount, 1);

    console.log('✅ BNB -> BANK swap 参数:');
    console.log(`   tokenIn: ${swapParams.tokenIn}`);
    console.log(`   tokenOut: ${swapParams.tokenOut}`);
    console.log(`   fee: ${swapParams.fee / 10000}%`);
    console.log(`   amountIn: ${formatEther(swapParams.amountIn)} BNB`);
    console.log(`   amountOutMinimum: ${formatEther(swapParams.amountOutMinimum)} BANK`);
    console.log(`   deadline: ${new Date(Number(swapParams.deadline) * 1000).toLocaleString()}`);
  } catch (error) {
    console.log(`❌ 创建 swap 参数失败: ${error}`);
  }

  // 5. 安全提示
  console.log('\n⚠️ 安全提示:');
  console.log('   - 这是测试功能，请在测试网络上使用');
  console.log('   - 确保有足够的 BNB 支付 gas 费用');
  console.log('   - BANK 代币流动性较低，可能有较大滑点');
  console.log('   - 建议先用小额测试');

  console.log('\n✅ PancakeSwap V3 Router 服务测试完成');
}
