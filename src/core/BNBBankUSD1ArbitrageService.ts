import type { Address } from 'viem';
import { formatEther, parseEther } from 'viem';
import { BSC_CONTRACTS } from '../constants/contracts';
import { getTokenAddress } from '../constants/tokens';
import { DirectV3PoolSwapService } from './DirectV3PoolSwapService';
import { UnifiedDexService } from './UnifiedDexService';

/**
 * 三角套利步骤结果
 */
export interface ArbitrageStepResult {
  step: number;
  description: string;
  inputToken: string;
  outputToken: string;
  inputAmount: bigint;
  outputAmount: bigint;
  exchangeRate: string;
  dexVersion: string;
  fee?: number;
  priceImpact?: number;
  success: boolean;
  error?: string;
}

/**
 * 三角套利分析结果
 */
export interface TriangularArbitrageAnalysis {
  path: string[];
  inputAmount: bigint;
  steps: ArbitrageStepResult[];
  finalOutputAmount: bigint;
  profit: bigint;
  profitPercentage: number;
  estimatedGasCostUSD: number;
  netProfitUSD: number;
  isProfitable: boolean;
  recommendation: 'EXECUTE' | 'MONITOR' | 'SKIP';
  warnings: string[];
  timestamp: Date;
}

/**
 * BNB-BANK-USD1 三角套利专用服务
 * 路径: BNB -> BANK -> USD1 -> BNB
 */
export class BNBBankUSD1ArbitrageService {
  private readonly bnbAddress: Address;
  private readonly bankAddress: Address;
  private readonly usd1Address: Address;
  private readonly directV3Service: DirectV3PoolSwapService;
  private readonly unifiedDexService: UnifiedDexService;

  constructor() {
    this.bnbAddress = getTokenAddress('BNB');
    this.bankAddress = getTokenAddress('BANK');
    this.usd1Address = getTokenAddress('USD1');
    this.directV3Service = new DirectV3PoolSwapService();
    this.unifiedDexService = new UnifiedDexService();
  }

  /**
   * 分析完整的三角套利机会
   */
  async analyzeTriangularArbitrage(inputAmountBNB: bigint): Promise<TriangularArbitrageAnalysis> {
    console.log('🔍 分析三角套利路径: BNB -> BANK -> USD1 -> BNB');
    console.log(`   输入金额: ${formatEther(inputAmountBNB)} BNB\n`);

    const steps: ArbitrageStepResult[] = [];
    const warnings: string[] = [];
    let currentAmount = inputAmountBNB;
    let allStepsSuccessful = true;

    // 步骤 1: BNB -> BANK
    console.log('📊 步骤 1: BNB -> BANK');
    const step1 = await this.executeBNBToBANK(currentAmount);
    steps.push(step1);

    if (step1.success) {
      currentAmount = step1.outputAmount;
      console.log(`✅ 成功: ${formatEther(inputAmountBNB)} BNB -> ${formatEther(currentAmount)} BANK`);
      console.log(`   汇率: ${step1.exchangeRate}`);
      console.log(`   DEX: ${step1.dexVersion}\n`);
    } else {
      console.log(`❌ 失败: ${step1.error}\n`);
      allStepsSuccessful = false;
    }

    // 步骤 2: BANK -> USD1
    console.log('📊 步骤 2: BANK -> USD1');
    const step2 = await this.executeBANKToUSD1(currentAmount);
    steps.push(step2);

    if (step2.success && allStepsSuccessful) {
      currentAmount = step2.outputAmount;
      console.log(`✅ 成功: ${formatEther(step1.outputAmount)} BANK -> ${formatEther(currentAmount)} USD1`);
      console.log(`   汇率: ${step2.exchangeRate}`);
      console.log(`   DEX: ${step2.dexVersion}`);
      if (step2.priceImpact) {
        console.log(`   价格影响: ${step2.priceImpact.toFixed(4)}%`);
      }
      console.log('');
    } else {
      console.log(`❌ 失败: ${step2.error}\n`);
      allStepsSuccessful = false;
    }

    // 步骤 3: USD1 -> BNB
    console.log('📊 步骤 3: USD1 -> BNB');
    const step3 = await this.executeUSD1ToBNB(currentAmount);
    steps.push(step3);

    if (step3.success && allStepsSuccessful) {
      currentAmount = step3.outputAmount;
      console.log(`✅ 成功: ${formatEther(step2.outputAmount)} USD1 -> ${formatEther(currentAmount)} BNB`);
      console.log(`   汇率: ${step3.exchangeRate}`);
      console.log(`   DEX: ${step3.dexVersion}\n`);
    } else {
      console.log(`❌ 失败: ${step3.error}\n`);
      allStepsSuccessful = false;
    }

    // 计算利润和分析
    const finalOutputAmount = allStepsSuccessful ? currentAmount : 0n;
    const profit = finalOutputAmount > inputAmountBNB ? finalOutputAmount - inputAmountBNB : 0n;
    const profitPercentage = inputAmountBNB > 0 ? Number((profit * 10000n) / inputAmountBNB) / 100 : 0;

    // 估算 Gas 费用 (3个交易的总费用)
    const estimatedGasCostUSD = this.estimateGasCost();

    // 估算净利润 (假设 BNB = $600)
    const bnbPriceUSD = 600;
    const grossProfitUSD = Number(formatEther(profit)) * bnbPriceUSD;
    const netProfitUSD = grossProfitUSD - estimatedGasCostUSD;

    // 生成建议
    let recommendation: 'EXECUTE' | 'MONITOR' | 'SKIP' = 'SKIP';
    if (allStepsSuccessful && netProfitUSD > 5) {
      recommendation = 'EXECUTE';
    } else if (allStepsSuccessful && netProfitUSD > 0) {
      recommendation = 'MONITOR';
    }

    // 生成警告
    if (!allStepsSuccessful) {
      warnings.push('部分交易步骤失败，无法完成套利');
    }
    if (netProfitUSD < 0) {
      warnings.push('预期净利润为负，不建议执行');
    }
    if (profitPercentage < 0.1) {
      warnings.push('利润率过低，可能不值得执行');
    }

    return {
      path: ['BNB', 'BANK', 'USD1', 'BNB'],
      inputAmount: inputAmountBNB,
      steps,
      finalOutputAmount,
      profit,
      profitPercentage,
      estimatedGasCostUSD,
      netProfitUSD,
      isProfitable: netProfitUSD > 0,
      recommendation,
      warnings,
      timestamp: new Date(),
    };
  }

  /**
   * 步骤 1: BNB -> BANK (使用 V3 直接池子)
   */
  private async executeBNBToBANK(bnbAmount: bigint): Promise<ArbitrageStepResult> {
    try {
      // 使用我们的 DirectV3PoolSwapService
      // 根据验证结果：Token0=BANK, Token1=WBNB
      // BNB -> BANK 需要 zeroForOne=false (WBNB -> BANK)
      const simulation = await this.directV3Service.simulateSwap(
        BSC_CONTRACTS.BNB_BANK_POOL as Address, // BNB/BANK 池子地址
        false, // zeroForOne=false: WBNB -> BANK
        bnbAmount,
      );

      if (!simulation) {
        return {
          step: 1,
          description: 'BNB -> BANK',
          inputToken: 'BNB',
          outputToken: 'BANK',
          inputAmount: bnbAmount,
          outputAmount: 0n,
          exchangeRate: 'N/A',
          dexVersion: 'N/A',
          success: false,
          error: '无法模拟 BNB -> BANK 交易',
        };
      }

      // zeroForOne=false 时，amount0 是输出（负数），amount1 是输入（正数）
      const outputAmount = simulation.estimatedAmount0 < 0 ? -simulation.estimatedAmount0 : simulation.estimatedAmount0;
      const exchangeRate = `${formatEther(bnbAmount)} BNB = ${formatEther(outputAmount)} BANK`;

      return {
        step: 1,
        description: 'BNB -> BANK',
        inputToken: 'BNB',
        outputToken: 'BANK',
        inputAmount: bnbAmount,
        outputAmount,
        exchangeRate,
        dexVersion: 'PancakeSwap V3 (Direct Pool)',
        priceImpact: simulation.priceImpact,
        success: true,
      };
    } catch (error) {
      return {
        step: 1,
        description: 'BNB -> BANK',
        inputToken: 'BNB',
        outputToken: 'BANK',
        inputAmount: bnbAmount,
        outputAmount: 0n,
        exchangeRate: 'N/A',
        dexVersion: 'N/A',
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 步骤 2: BANK -> USD1 (使用 V3 直接池子)
   */
  private async executeBANKToUSD1(bankAmount: bigint): Promise<ArbitrageStepResult> {
    try {
      // 使用我们的 DirectV3PoolSwapService
      const simulation = await this.directV3Service.simulateSwap(
        BSC_CONTRACTS.USD1_BANK_POOL as Address, // USD1/BANK 池子地址
        true, // zeroForOne: BANK -> USD1
        bankAmount,
      );

      if (!simulation) {
        return {
          step: 2,
          description: 'BANK -> USD1',
          inputToken: 'BANK',
          outputToken: 'USD1',
          inputAmount: bankAmount,
          outputAmount: 0n,
          exchangeRate: 'N/A',
          dexVersion: 'N/A',
          success: false,
          error: '无法模拟 BANK -> USD1 交易',
        };
      }

      const outputAmount = simulation.estimatedAmount1 < 0 ? -simulation.estimatedAmount1 : simulation.estimatedAmount1;
      const exchangeRate = `${formatEther(bankAmount)} BANK = ${formatEther(outputAmount)} USD1`;

      return {
        step: 2,
        description: 'BANK -> USD1',
        inputToken: 'BANK',
        outputToken: 'USD1',
        inputAmount: bankAmount,
        outputAmount,
        exchangeRate,
        dexVersion: 'PancakeSwap V3 (Direct Pool)',
        priceImpact: simulation.priceImpact,
        success: true,
      };
    } catch (error) {
      return {
        step: 2,
        description: 'BANK -> USD1',
        inputToken: 'BANK',
        outputToken: 'USD1',
        inputAmount: bankAmount,
        outputAmount: 0n,
        exchangeRate: 'N/A',
        dexVersion: 'N/A',
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 步骤 3: USD1 -> BNB (使用 V3 直接池子)
   */
  private async executeUSD1ToBNB(usd1Amount: bigint): Promise<ArbitrageStepResult> {
    try {
      // 使用我们的 DirectV3PoolSwapService
      // 根据验证结果：Token0=USD1, Token1=WBNB
      // USD1 -> BNB 需要 zeroForOne=true (USD1 -> WBNB)
      const simulation = await this.directV3Service.simulateSwap(
        BSC_CONTRACTS.BNB_USD1_POOL as Address, // BNB/USD1 池子地址
        true, // zeroForOne=true: USD1 -> WBNB
        usd1Amount,
      );

      if (!simulation) {
        return {
          step: 3,
          description: 'USD1 -> BNB',
          inputToken: 'USD1',
          outputToken: 'BNB',
          inputAmount: usd1Amount,
          outputAmount: 0n,
          exchangeRate: 'N/A',
          dexVersion: 'N/A',
          success: false,
          error: '无法模拟 USD1 -> BNB 交易',
        };
      }

      // zeroForOne=true 时，amount0 是输入（正数），amount1 是输出（负数）
      const outputAmount = simulation.estimatedAmount1 < 0 ? -simulation.estimatedAmount1 : simulation.estimatedAmount1;
      const exchangeRate = `${formatEther(usd1Amount)} USD1 = ${formatEther(outputAmount)} BNB`;

      return {
        step: 3,
        description: 'USD1 -> BNB',
        inputToken: 'USD1',
        outputToken: 'BNB',
        inputAmount: usd1Amount,
        outputAmount,
        exchangeRate,
        dexVersion: 'PancakeSwap V3 (Direct Pool)',
        priceImpact: simulation.priceImpact,
        success: true,
      };
    } catch (error) {
      return {
        step: 3,
        description: 'USD1 -> BNB',
        inputToken: 'USD1',
        outputToken: 'BNB',
        inputAmount: usd1Amount,
        outputAmount: 0n,
        exchangeRate: 'N/A',
        dexVersion: 'N/A',
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 估算 Gas 费用
   */
  private estimateGasCost(): number {
    // 估算三个交易的总 Gas 费用
    // V2 swap: ~150,000 gas
    // V3 swap: ~200,000 gas
    // 总计: ~500,000 gas
    const totalGas = 500000;
    const gasPriceGwei = 1; // 1 Gwei
    const bnbPriceUSD = 600; // $600 per BNB

    const gasCostBNB = (totalGas * gasPriceGwei) / 1e9; // Convert to BNB
    return gasCostBNB * bnbPriceUSD; // Convert to USD
  }

  /**
   * 显示分析结果
   */
  displayAnalysisResult(analysis: TriangularArbitrageAnalysis): void {
    console.log(`\n${'='.repeat(60)}`);
    console.log('📈 三角套利分析结果');
    console.log('='.repeat(60));

    console.log(`🔄 路径: ${analysis.path.join(' -> ')}`);
    console.log(`💰 输入金额: ${formatEther(analysis.inputAmount)} BNB`);
    console.log(`💰 最终输出: ${formatEther(analysis.finalOutputAmount)} BNB`);
    console.log(`📊 原始利润: ${formatEther(analysis.profit)} BNB (${analysis.profitPercentage.toFixed(4)}%)`);
    console.log(`⛽ 预估 Gas 费: $${analysis.estimatedGasCostUSD.toFixed(2)}`);
    console.log(`💵 净利润: $${analysis.netProfitUSD.toFixed(2)}`);
    console.log(`✅ 是否盈利: ${analysis.isProfitable ? '是' : '否'}`);
    console.log(`🎯 建议: ${analysis.recommendation}`);

    if (analysis.warnings.length > 0) {
      console.log('⚠️ 警告:');
      for (const warning of analysis.warnings) {
        console.log(`   - ${warning}`);
      }
    }

    console.log('\n📋 详细步骤:');
    for (const step of analysis.steps) {
      const status = step.success ? '✅' : '❌';
      console.log(`   ${status} 步骤 ${step.step}: ${step.description}`);
      if (step.success) {
        console.log(`      汇率: ${step.exchangeRate}`);
        console.log(`      DEX: ${step.dexVersion}`);
        if (step.priceImpact) {
          console.log(`      价格影响: ${step.priceImpact.toFixed(4)}%`);
        }
      } else {
        console.log(`      错误: ${step.error}`);
      }
    }

    console.log(`\n${'='.repeat(60)}`);
  }

  /**
   * 分析反向三角套利机会 (BNB -> USD1 -> BANK -> BNB)
   */
  async analyzeReverseTriangularArbitrage(inputAmountBNB: bigint): Promise<TriangularArbitrageAnalysis> {
    console.log('🔄 分析反向三角套利路径: BNB -> USD1 -> BANK -> BNB');
    console.log(`   输入金额: ${formatEther(inputAmountBNB)} BNB\n`);

    const steps: ArbitrageStepResult[] = [];
    const warnings: string[] = [];
    let currentAmount = inputAmountBNB;
    let allStepsSuccessful = true;

    // 步骤 1: BNB -> USD1
    console.log('📊 步骤 1: BNB -> USD1');
    const step1 = await this.executeBNBToUSD1(currentAmount);
    steps.push(step1);

    if (step1.success) {
      currentAmount = step1.outputAmount;
      console.log(`✅ 成功: ${formatEther(inputAmountBNB)} BNB -> ${formatEther(currentAmount)} USD1`);
      console.log(`   汇率: ${step1.exchangeRate}`);
      console.log(`   DEX: ${step1.dexVersion}\n`);
    } else {
      console.log(`❌ 失败: ${step1.error}\n`);
      allStepsSuccessful = false;
    }

    // 步骤 2: USD1 -> BANK
    console.log('📊 步骤 2: USD1 -> BANK');
    const step2 = await this.executeUSD1ToBANK(currentAmount);
    steps.push(step2);

    if (step2.success && allStepsSuccessful) {
      currentAmount = step2.outputAmount;
      console.log(`✅ 成功: ${formatEther(step1.outputAmount)} USD1 -> ${formatEther(currentAmount)} BANK`);
      console.log(`   汇率: ${step2.exchangeRate}`);
      console.log(`   DEX: ${step2.dexVersion}`);
      if (step2.priceImpact) {
        console.log(`   价格影响: ${step2.priceImpact.toFixed(4)}%`);
      }
      console.log('');
    } else {
      console.log(`❌ 失败: ${step2.error}\n`);
      allStepsSuccessful = false;
    }

    // 步骤 3: BANK -> BNB
    console.log('📊 步骤 3: BANK -> BNB');
    const step3 = await this.executeBANKToBNB(currentAmount);
    steps.push(step3);

    if (step3.success && allStepsSuccessful) {
      currentAmount = step3.outputAmount;
      console.log(`✅ 成功: ${formatEther(step2.outputAmount)} BANK -> ${formatEther(currentAmount)} BNB`);
      console.log(`   汇率: ${step3.exchangeRate}`);
      console.log(`   DEX: ${step3.dexVersion}\n`);
    } else {
      console.log(`❌ 失败: ${step3.error}\n`);
      allStepsSuccessful = false;
    }

    // 计算利润和分析
    const finalOutputAmount = allStepsSuccessful ? currentAmount : 0n;
    const profit = finalOutputAmount > inputAmountBNB ? finalOutputAmount - inputAmountBNB : 0n;
    const profitPercentage = inputAmountBNB > 0 ? Number((profit * 10000n) / inputAmountBNB) / 100 : 0;

    // 估算 Gas 费用 (3个交易的总费用)
    const estimatedGasCostUSD = this.estimateGasCost();

    // 估算净利润 (假设 BNB = $600)
    const bnbPriceUSD = 600;
    const grossProfitUSD = Number(formatEther(profit)) * bnbPriceUSD;
    const netProfitUSD = grossProfitUSD - estimatedGasCostUSD;

    // 生成建议
    let recommendation: 'EXECUTE' | 'MONITOR' | 'SKIP' = 'SKIP';
    if (allStepsSuccessful && netProfitUSD > 5) {
      recommendation = 'EXECUTE';
    } else if (allStepsSuccessful && netProfitUSD > 0) {
      recommendation = 'MONITOR';
    }

    // 生成警告
    if (!allStepsSuccessful) {
      warnings.push('部分交易步骤失败，无法完成套利');
    }
    if (netProfitUSD < 0) {
      warnings.push('预期净利润为负，不建议执行');
    }
    if (profitPercentage < 0.1) {
      warnings.push('利润率过低，可能不值得执行');
    }

    return {
      path: ['BNB', 'USD1', 'BANK', 'BNB'],
      inputAmount: inputAmountBNB,
      steps,
      finalOutputAmount,
      profit,
      profitPercentage,
      estimatedGasCostUSD,
      netProfitUSD,
      isProfitable: netProfitUSD > 0,
      recommendation,
      warnings,
      timestamp: new Date(),
    };
  }

  /**
   * 步骤 1 (反向): BNB -> USD1 (使用 V3 直接池子)
   */
  private async executeBNBToUSD1(bnbAmount: bigint): Promise<ArbitrageStepResult> {
    try {
      // 根据验证结果：Token0=USD1, Token1=WBNB
      // BNB -> USD1 需要 zeroForOne=false (WBNB -> USD1)
      const simulation = await this.directV3Service.simulateSwap(
        BSC_CONTRACTS.BNB_USD1_POOL as Address, // BNB/USD1 池子地址
        false, // zeroForOne=false: WBNB -> USD1
        bnbAmount,
      );

      if (!simulation) {
        return {
          step: 1,
          description: 'BNB -> USD1',
          inputToken: 'BNB',
          outputToken: 'USD1',
          inputAmount: bnbAmount,
          outputAmount: 0n,
          exchangeRate: 'N/A',
          dexVersion: 'N/A',
          success: false,
          error: '无法模拟 BNB -> USD1 交易',
        };
      }

      // zeroForOne=false 时，amount0 是输出（负数），amount1 是输入（正数）
      const outputAmount = simulation.estimatedAmount0 < 0 ? -simulation.estimatedAmount0 : simulation.estimatedAmount0;
      const exchangeRate = `${formatEther(bnbAmount)} BNB = ${formatEther(outputAmount)} USD1`;

      return {
        step: 1,
        description: 'BNB -> USD1',
        inputToken: 'BNB',
        outputToken: 'USD1',
        inputAmount: bnbAmount,
        outputAmount,
        exchangeRate,
        dexVersion: 'PancakeSwap V3 (Direct Pool)',
        priceImpact: simulation.priceImpact,
        success: true,
      };
    } catch (error) {
      return {
        step: 1,
        description: 'BNB -> USD1',
        inputToken: 'BNB',
        outputToken: 'USD1',
        inputAmount: bnbAmount,
        outputAmount: 0n,
        exchangeRate: 'N/A',
        dexVersion: 'N/A',
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 步骤 2 (反向): USD1 -> BANK (使用 V3 直接池子)
   */
  private async executeUSD1ToBANK(usd1Amount: bigint): Promise<ArbitrageStepResult> {
    try {
      // 根据验证结果：Token0=BANK, Token1=USD1
      // USD1 -> BANK 需要 zeroForOne=false (USD1 -> BANK)
      const simulation = await this.directV3Service.simulateSwap(
        BSC_CONTRACTS.USD1_BANK_POOL as Address, // USD1/BANK 池子地址
        false, // zeroForOne=false: USD1 -> BANK
        usd1Amount,
      );

      if (!simulation) {
        return {
          step: 2,
          description: 'USD1 -> BANK',
          inputToken: 'USD1',
          outputToken: 'BANK',
          inputAmount: usd1Amount,
          outputAmount: 0n,
          exchangeRate: 'N/A',
          dexVersion: 'N/A',
          success: false,
          error: '无法模拟 USD1 -> BANK 交易',
        };
      }

      // zeroForOne=false 时，amount0 是输出（负数），amount1 是输入（正数）
      const outputAmount = simulation.estimatedAmount0 < 0 ? -simulation.estimatedAmount0 : simulation.estimatedAmount0;
      const exchangeRate = `${formatEther(usd1Amount)} USD1 = ${formatEther(outputAmount)} BANK`;

      return {
        step: 2,
        description: 'USD1 -> BANK',
        inputToken: 'USD1',
        outputToken: 'BANK',
        inputAmount: usd1Amount,
        outputAmount,
        exchangeRate,
        dexVersion: 'PancakeSwap V3 (Direct Pool)',
        priceImpact: simulation.priceImpact,
        success: true,
      };
    } catch (error) {
      return {
        step: 2,
        description: 'USD1 -> BANK',
        inputToken: 'USD1',
        outputToken: 'BANK',
        inputAmount: usd1Amount,
        outputAmount: 0n,
        exchangeRate: 'N/A',
        dexVersion: 'N/A',
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 步骤 3 (反向): BANK -> BNB (使用 V3 直接池子)
   */
  private async executeBANKToBNB(bankAmount: bigint): Promise<ArbitrageStepResult> {
    try {
      // 根据验证结果：Token0=BANK, Token1=WBNB
      // BANK -> BNB 需要 zeroForOne=true (BANK -> WBNB)
      const simulation = await this.directV3Service.simulateSwap(
        BSC_CONTRACTS.BNB_BANK_POOL as Address, // BNB/BANK 池子地址
        true, // zeroForOne=true: BANK -> WBNB
        bankAmount,
      );

      if (!simulation) {
        return {
          step: 3,
          description: 'BANK -> BNB',
          inputToken: 'BANK',
          outputToken: 'BNB',
          inputAmount: bankAmount,
          outputAmount: 0n,
          exchangeRate: 'N/A',
          dexVersion: 'N/A',
          success: false,
          error: '无法模拟 BANK -> BNB 交易',
        };
      }

      // zeroForOne=true 时，amount0 是输入（正数），amount1 是输出（负数）
      const outputAmount = simulation.estimatedAmount1 < 0 ? -simulation.estimatedAmount1 : simulation.estimatedAmount1;
      const exchangeRate = `${formatEther(bankAmount)} BANK = ${formatEther(outputAmount)} BNB`;

      return {
        step: 3,
        description: 'BANK -> BNB',
        inputToken: 'BANK',
        outputToken: 'BNB',
        inputAmount: bankAmount,
        outputAmount,
        exchangeRate,
        dexVersion: 'PancakeSwap V3 (Direct Pool)',
        priceImpact: simulation.priceImpact,
        success: true,
      };
    } catch (error) {
      return {
        step: 3,
        description: 'BANK -> BNB',
        inputToken: 'BANK',
        outputToken: 'BNB',
        inputAmount: bankAmount,
        outputAmount: 0n,
        exchangeRate: 'N/A',
        dexVersion: 'N/A',
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }
}

/**
 * 测试 BNB-BANK-USD1 三角套利（包含正向和反向路径）
 */
export async function testBNBBankUSD1Arbitrage(): Promise<void> {
  console.log('🚀 测试 BNB-BANK-USD1 三角套利机会（正向和反向路径）...\n');

  const service = new BNBBankUSD1ArbitrageService();

  // 测试不同的输入金额
  const testAmounts = [
    parseEther('0.1'), // 0.1 BNB
    parseEther('1'), // 1 BNB
    parseEther('10'), // 10 BNB
  ];

  for (const amount of testAmounts) {
    console.log(`\n${'='.repeat(100)}`);
    console.log(`🧪 测试金额: ${formatEther(amount)} BNB`);
    console.log(`${'='.repeat(100)}`);

    try {
      // 测试正向路径
      console.log('\n📈 正向路径: BNB → BANK → USD1 → BNB');
      console.log(`${'─'.repeat(80)}`);
      const forwardAnalysis = await service.analyzeTriangularArbitrage(amount);
      service.displayAnalysisResult(forwardAnalysis);

      // 测试反向路径
      console.log('\n📉 反向路径: BNB → USD1 → BANK → BNB');
      console.log(`${'─'.repeat(80)}`);
      const reverseAnalysis = await service.analyzeReverseTriangularArbitrage(amount);
      service.displayAnalysisResult(reverseAnalysis);

      // 对比分析
      console.log('\n🔍 路径对比分析:');
      console.log(`${'─'.repeat(50)}`);
      console.log(
        `正向路径利润: ${forwardAnalysis.profitPercentage.toFixed(4)}% (${forwardAnalysis.netProfitUSD.toFixed(2)} USD)`,
      );
      console.log(
        `反向路径利润: ${reverseAnalysis.profitPercentage.toFixed(4)}% (${reverseAnalysis.netProfitUSD.toFixed(2)} USD)`,
      );

      if (forwardAnalysis.netProfitUSD > reverseAnalysis.netProfitUSD) {
        console.log(
          `🏆 推荐: 正向路径更优 (多赚 ${(forwardAnalysis.netProfitUSD - reverseAnalysis.netProfitUSD).toFixed(2)} USD)`,
        );
      } else if (reverseAnalysis.netProfitUSD > forwardAnalysis.netProfitUSD) {
        console.log(
          `🏆 推荐: 反向路径更优 (多赚 ${(reverseAnalysis.netProfitUSD - forwardAnalysis.netProfitUSD).toFixed(2)} USD)`,
        );
      } else {
        console.log('⚖️ 两个路径利润相当');
      }
    } catch (error) {
      console.error(`❌ 分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }

    // 添加延迟以避免 RPC 限制
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  console.log('\n✅ BNB-BANK-USD1 三角套利测试完成（正向和反向路径）');
}
