import type { Address } from 'viem';

/**
 * 缓存项接口
 */
export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // 生存时间（毫秒）
  accessCount: number;
  lastAccess: number;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  totalItems: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  memoryUsage: number; // 估算的内存使用量（字节）
}

/**
 * 代币信息缓存数据
 */
export interface TokenCacheData {
  symbol: string;
  decimals: number;
  name?: string;
}

/**
 * 池子状态缓存数据
 */
export interface PoolStateCacheData {
  token0: Address;
  token1: Address;
  fee: number;
  liquidity: bigint;
  sqrtPriceX96: bigint;
  tick: number;
  unlocked: boolean;
}

/**
 * 智能缓存管理器
 * 提供多层缓存架构和智能缓存策略
 */
export class SmartCacheManager {
  // 代币信息缓存（永久缓存，很少变化）
  private tokenCache = new Map<Address, CacheItem<TokenCacheData>>();

  // 池子状态缓存（短期缓存，频繁变化）
  private poolStateCache = new Map<Address, CacheItem<PoolStateCacheData>>();

  // 价格计算结果缓存（超短期缓存）
  private priceCalculationCache = new Map<string, CacheItem<bigint>>();

  // 缓存统计
  private stats = {
    token: { hits: 0, misses: 0 },
    poolState: { hits: 0, misses: 0 },
    priceCalculation: { hits: 0, misses: 0 },
  };

  // 缓存配置
  private readonly config = {
    tokenTTL: 24 * 60 * 60 * 1000, // 代币信息：24小时
    poolStateTTL: 5 * 1000, // 池子状态：5秒
    priceCalculationTTL: 1000, // 价格计算：1秒
    maxTokenCacheSize: 1000,
    maxPoolStateCacheSize: 100,
    maxPriceCalculationCacheSize: 500,
    cleanupInterval: 60 * 1000, // 清理间隔：1分钟
  };

  private cleanupTimer?: NodeJS.Timeout;

  constructor() {
    this.startCleanupTimer();
  }

  /**
   * 获取代币信息（永久缓存）
   */
  getTokenInfo(tokenAddress: Address): TokenCacheData | null {
    const item = this.tokenCache.get(tokenAddress);

    if (item && this.isValid(item)) {
      this.updateAccessStats(item);
      this.stats.token.hits++;
      return item.data;
    }

    this.stats.token.misses++;
    return null;
  }

  /**
   * 设置代币信息缓存
   */
  setTokenInfo(tokenAddress: Address, data: TokenCacheData): void {
    const item: CacheItem<TokenCacheData> = {
      data,
      timestamp: Date.now(),
      ttl: this.config.tokenTTL,
      accessCount: 1,
      lastAccess: Date.now(),
    };

    this.tokenCache.set(tokenAddress, item);
    this.enforceMaxSize(this.tokenCache, this.config.maxTokenCacheSize);
  }

  /**
   * 获取池子状态（短期缓存）
   */
  getPoolState(poolAddress: Address): PoolStateCacheData | null {
    const item = this.poolStateCache.get(poolAddress);

    if (item && this.isValid(item)) {
      this.updateAccessStats(item);
      this.stats.poolState.hits++;
      return item.data;
    }

    this.stats.poolState.misses++;
    return null;
  }

  /**
   * 设置池子状态缓存
   */
  setPoolState(poolAddress: Address, data: PoolStateCacheData): void {
    const item: CacheItem<PoolStateCacheData> = {
      data,
      timestamp: Date.now(),
      ttl: this.config.poolStateTTL,
      accessCount: 1,
      lastAccess: Date.now(),
    };

    this.poolStateCache.set(poolAddress, item);
    this.enforceMaxSize(this.poolStateCache, this.config.maxPoolStateCacheSize);
  }

  /**
   * 获取价格计算结果（超短期缓存）
   */
  getPriceCalculation(key: string): bigint | null {
    const item = this.priceCalculationCache.get(key);

    if (item && this.isValid(item)) {
      this.updateAccessStats(item);
      this.stats.priceCalculation.hits++;
      return item.data;
    }

    this.stats.priceCalculation.misses++;
    return null;
  }

  /**
   * 设置价格计算结果缓存
   */
  setPriceCalculation(key: string, result: bigint): void {
    const item: CacheItem<bigint> = {
      data: result,
      timestamp: Date.now(),
      ttl: this.config.priceCalculationTTL,
      accessCount: 1,
      lastAccess: Date.now(),
    };

    this.priceCalculationCache.set(key, item);
    this.enforceMaxSize(this.priceCalculationCache, this.config.maxPriceCalculationCacheSize);
  }

  /**
   * 生成价格计算缓存键
   */
  generatePriceCalculationKey(poolAddress: Address, zeroForOne: boolean, amountIn: bigint): string {
    return `${poolAddress}_${zeroForOne}_${amountIn.toString()}`;
  }

  /**
   * 批量预热缓存
   */
  async warmupCache(
    tokenAddresses: Address[],
    poolAddresses: Address[],
    dataProvider: {
      getTokenInfo: (address: Address) => Promise<TokenCacheData>;
      getPoolState: (address: Address) => Promise<PoolStateCacheData>;
    },
  ): Promise<void> {
    console.log(`🔥 开始缓存预热: ${tokenAddresses.length} 个代币, ${poolAddresses.length} 个池子`);

    const startTime = Date.now();

    // 并行预热代币信息
    const tokenPromises = tokenAddresses.map(async (address) => {
      if (!this.getTokenInfo(address)) {
        try {
          const data = await dataProvider.getTokenInfo(address);
          this.setTokenInfo(address, data);
        } catch (error) {
          console.warn(`预热代币信息失败: ${address}`, error);
        }
      }
    });

    // 并行预热池子状态
    const poolPromises = poolAddresses.map(async (address) => {
      if (!this.getPoolState(address)) {
        try {
          const data = await dataProvider.getPoolState(address);
          this.setPoolState(address, data);
        } catch (error) {
          console.warn(`预热池子状态失败: ${address}`, error);
        }
      }
    });

    await Promise.allSettled([...tokenPromises, ...poolPromises]);

    const duration = Date.now() - startTime;
    console.log(`🔥 缓存预热完成，耗时 ${duration}ms`);
  }

  /**
   * 强制刷新池子状态缓存
   */
  invalidatePoolState(poolAddress: Address): void {
    this.poolStateCache.delete(poolAddress);
  }

  /**
   * 强制刷新所有池子状态缓存
   */
  invalidateAllPoolStates(): void {
    this.poolStateCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    token: CacheStats;
    poolState: CacheStats;
    priceCalculation: CacheStats;
    total: CacheStats;
  } {
    const tokenStats = this.calculateCacheStats(this.tokenCache, this.stats.token);
    const poolStateStats = this.calculateCacheStats(this.poolStateCache, this.stats.poolState);
    const priceCalculationStats = this.calculateCacheStats(this.priceCalculationCache, this.stats.priceCalculation);

    const totalStats: CacheStats = {
      totalItems: tokenStats.totalItems + poolStateStats.totalItems + priceCalculationStats.totalItems,
      hitCount: tokenStats.hitCount + poolStateStats.hitCount + priceCalculationStats.hitCount,
      missCount: tokenStats.missCount + poolStateStats.missCount + priceCalculationStats.missCount,
      hitRate: 0,
      memoryUsage: tokenStats.memoryUsage + poolStateStats.memoryUsage + priceCalculationStats.memoryUsage,
    };

    totalStats.hitRate =
      totalStats.hitCount + totalStats.missCount > 0
        ? totalStats.hitCount / (totalStats.hitCount + totalStats.missCount)
        : 0;

    return {
      token: tokenStats,
      poolState: poolStateStats,
      priceCalculation: priceCalculationStats,
      total: totalStats,
    };
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    // 清理代币缓存
    for (const [key, item] of this.tokenCache) {
      if (!this.isValid(item, now)) {
        this.tokenCache.delete(key);
        cleanedCount++;
      }
    }

    // 清理池子状态缓存
    for (const [key, item] of this.poolStateCache) {
      if (!this.isValid(item, now)) {
        this.poolStateCache.delete(key);
        cleanedCount++;
      }
    }

    // 清理价格计算缓存
    for (const [key, item] of this.priceCalculationCache) {
      if (!this.isValid(item, now)) {
        this.priceCalculationCache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个过期缓存项`);
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    this.tokenCache.clear();
    this.poolStateCache.clear();
    this.priceCalculationCache.clear();

    console.log('🗑️ 智能缓存管理器已销毁');
  }

  /**
   * 检查缓存项是否有效
   */
  private isValid(item: CacheItem<unknown>, now = Date.now()): boolean {
    return now - item.timestamp < item.ttl;
  }

  /**
   * 更新访问统计
   */
  private updateAccessStats(item: CacheItem<unknown>): void {
    item.accessCount++;
    item.lastAccess = Date.now();
  }

  /**
   * 强制执行最大缓存大小限制
   */
  private enforceMaxSize<T>(cache: Map<string, CacheItem<T>>, maxSize: number): void {
    if (cache.size <= maxSize) {
      return;
    }

    // 按最后访问时间排序，删除最久未访问的项
    const entries = Array.from(cache.entries());
    entries.sort((a, b) => a[1].lastAccess - b[1].lastAccess);

    const toDelete = entries.slice(0, cache.size - maxSize);
    for (const [key] of toDelete) {
      cache.delete(key);
    }
  }

  /**
   * 计算缓存统计信息
   */
  private calculateCacheStats<T>(
    cache: Map<string, CacheItem<T>>,
    stats: { hits: number; misses: number },
  ): CacheStats {
    const hitRate = stats.hits + stats.misses > 0 ? stats.hits / (stats.hits + stats.misses) : 0;

    // 估算内存使用量（简化计算）
    const memoryUsage = cache.size * 200; // 假设每个缓存项约200字节

    return {
      totalItems: cache.size,
      hitCount: stats.hits,
      missCount: stats.misses,
      hitRate,
      memoryUsage,
    };
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }
}

// 全局智能缓存管理器实例
export const smartCacheManager: SmartCacheManager = new SmartCacheManager();
