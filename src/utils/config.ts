import dotenv from 'dotenv';
import type { Address } from 'viem';
import { CONTRACTS } from '../constants/contracts.js';
import { getTokenAddress, isValidTokenSymbol } from '../constants/tokens.js';
dotenv.config();

function getEnvVariable(key: string, an_optional_default_value?: string): string {
  const value = process.env[key] || an_optional_default_value;
  if (value === undefined) {
    // Adjusted to be less strict for now, BlockchainService handles critical ones.
    // Consider making this stricter for production for certain keys.
    console.warn(`Warning: Environment variable ${key} is not set.`);
    return ''; // Return empty string or handle as appropriate
  }
  return value;
}

/**
 * 解析套利路径，支持代币符号和地址混合使用
 */
function parseArbitragePaths(pathsString: string): Address[][] {
  if (!pathsString) {
    console.warn('ARBITRAGE_PATHS not found in .env. Using default empty array.');
    return [];
  }

  try {
    const rawPaths = JSON.parse(pathsString);
    if (!Array.isArray(rawPaths)) {
      throw new Error('ARBITRAGE_PATHS must be an array');
    }

    const resolvedPaths: Address[][] = [];

    for (const path of rawPaths) {
      if (!Array.isArray(path)) {
        throw new Error('Each arbitrage path must be an array');
      }

      const resolvedPath: Address[] = [];
      for (const tokenIdentifier of path) {
        if (typeof tokenIdentifier !== 'string') {
          throw new Error('Token identifier must be a string');
        }

        let resolvedAddress: Address | null = null;

        // 检查是否为代币符号
        if (isValidTokenSymbol(tokenIdentifier)) {
          resolvedAddress = getTokenAddress(tokenIdentifier);
        }

        // 检查是否为地址格式
        if (!resolvedAddress && tokenIdentifier.startsWith('0x') && tokenIdentifier.length === 42) {
          resolvedAddress = tokenIdentifier as Address;
        }

        // 如果都不匹配，抛出错误
        if (!resolvedAddress) {
          throw new Error(`Invalid token identifier: ${tokenIdentifier}. Must be a valid token symbol or address.`);
        }

        resolvedPath.push(resolvedAddress);
      }

      resolvedPaths.push(resolvedPath);
    }

    console.log(`✅ 成功解析 ${resolvedPaths.length} 个套利路径`);
    resolvedPaths.forEach((path, index) => {
      console.log(
        `   路径 ${index + 1}: ${path.map((addr) => `${addr.slice(0, 6)}...${addr.slice(-4)}`).join(' -> ')}`,
      );
    });

    return resolvedPaths;
  } catch (error) {
    console.error('Failed to parse ARBITRAGE_PATHS from .env:', error);
    console.error('Example format: [["BNB", "BANK", "USD1", "BNB"]] or [["0x...", "0x...", "0x...", "0x..."]]');
    return [];
  }
}

export const BSC_RPC_URL: string = getEnvVariable('BSC_RPC_URL');
export const BSC_WS_RPC_URL: string = getEnvVariable('BSC_WS_RPC_URL');
export const PRIVATE_KEY: string = getEnvVariable('PRIVATE_KEY');
export const ALCHEMY_API_KEY: string = getEnvVariable('ALCHEMY_API_KEY'); // Though likely part of BSC_RPC_URL

export const PANCAKESWAP_ROUTER_ADDRESS: string = getEnvVariable(
  'PANCAKESWAP_ROUTER_ADDRESS',
  CONTRACTS.PANCAKESWAP_ROUTER,
);
export const ARBITRAGE_CONTRACT_ADDRESS: string = getEnvVariable('ARBITRAGE_CONTRACT_ADDRESS');

// 解析套利路径，支持代币符号
export const ARBITRAGE_PATHS: Address[][] = parseArbitragePaths(getEnvVariable('ARBITRAGE_PATHS'));

export const MIN_PROFIT_THRESHOLD_USD: number = Number.parseFloat(getEnvVariable('MIN_PROFIT_THRESHOLD_USD', '1.0'));
export const GAS_PRICE_GWEI: number = Number.parseInt(getEnvVariable('GAS_PRICE_GWEI', '5'), 10);
export const MAX_SLIPPAGE_PERCENT: number = Number.parseInt(getEnvVariable('MAX_SLIPPAGE_PERCENT', '1'), 10);

// Validate critical variables used by BlockchainService explicitly here as well
if (!BSC_RPC_URL) {
  throw new Error('CRITICAL: BSC_RPC_URL is not defined in the environment variables. Please check your .env file.');
}

if (!PRIVATE_KEY) {
  throw new Error('CRITICAL: PRIVATE_KEY is not defined in the environment variables. Please check your .env file.');
}

console.log('Configuration loaded from .env');
