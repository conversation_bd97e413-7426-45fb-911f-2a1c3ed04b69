{"$schema": "https://biomejs.dev/schemas/1.5.3/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true}, "ignore": ["dist", "node_modules"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 120, "lineEnding": "lf", "ignore": ["dist", "node_modules"]}, "vcs": {"enabled": true, "useIgnoreFile": true, "clientKind": "git"}, "javascript": {"formatter": {"indentStyle": "space", "indentWidth": 2, "lineWidth": 120, "quoteStyle": "single"}}}