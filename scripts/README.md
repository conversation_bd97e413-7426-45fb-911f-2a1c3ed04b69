# 合约部署脚本

## 概述

此目录包含用于编译和部署 BSC 三角套利智能合约的脚本。

## 文件说明

- `deploy.ts` - 主要的编译和部署脚本

## 使用方法

### 前置条件

1. 确保已配置 `.env` 文件，包含以下必需变量：

   ```
   BSC_RPC_URL=https://bnb-mainnet.g.alchemy.com/v2/YOUR_API_KEY
   PRIVATE_KEY=your_private_key_without_0x_prefix
   PANCAKESWAP_ROUTER_ADDRESS=0x10ED43C718714eb63d5aA57B78B54704E256024E
   ```

2. 确保钱包有足够的 BNB 用于支付部署 gas 费用

### 编译合约

仅编译合约而不部署：

```bash
npm run compile-contract
```

### 部署合约

编译并部署合约到 BSC 主网：

```bash
npm run deploy
```

部署成功后，合约地址将自动添加到 `.env` 文件中的 `ARBITRAGE_CONTRACT_ADDRESS` 变量。

## 注意事项

⚠️ **重要安全提醒**：

- 确保在测试网上充分测试后再部署到主网
- 私钥管理：生产环境中应使用更安全的密钥管理方案
- Gas 费用：部署可能需要较高的 gas 费用，请确保钱包余额充足

## 故障排除

### 常见问题

1. **编译错误**：检查 Solidity 合约语法和导入路径
2. **部署失败**：
   - 检查网络连接和 RPC URL
   - 确认钱包余额足够支付 gas 费用
   - 验证私钥格式正确
3. **Gas 估算错误**：可能需要调整 gas 限制或 gas 价格

### 调试

如果遇到问题，可以：

1. 检查控制台输出的详细错误信息
2. 在 BSCScan 上查看交易状态
3. 验证环境变量配置

## 下一步

部署成功后：

1. 在 BSCScan 上验证合约（可选）
2. 向合约转入用于套利的代币
3. 测试套利功能
4. 开始运行套利机器人
